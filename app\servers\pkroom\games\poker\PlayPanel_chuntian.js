var PlayPanel_chuntian = PlayLayer_PDK.extend({
    preloadAnimations: function() {
        //反春
        cc.spriteFrameCache.addSpriteFrames(
        "playing/chunTianTable/animate/fanchun/fanchun0.plist", 
        "playing/chunTianTable/animate/fanchun/fanchun0.png"
        );
        ccs.armatureDataManager.addArmatureFileInfo(
        "playing/chunTianTable/animate/fanchun/fanchun.ExportJson"
        );
        // 杀春
        cc.spriteFrameCache.addSpriteFrames(
        "playing/chunTianTable/animate/shachun/shachun0.plist", 
        "playing/chunTianTable/animate/shachun/shachun0.png"
        );
        ccs.armatureDataManager.addArmatureFileInfo(
        "playing/chunTianTable/animate/shachun/shachun.ExportJson"
        );
        // 春天
        cc.spriteFrameCache.addSpriteFrames(
            "playing/chunTianTable/animate/chuntian/chuntian0.plist", 
            "playing/chunTianTable/animate/chuntian/chuntian0.png"
        );
        ccs.armatureDataManager.addArmatureFileInfo(
            "playing/chunTianTable/animate/chuntian/chuntian.ExportJson"
        );
        // 自然春
        cc.spriteFrameCache.addSpriteFrames(
            "playing/chunTianTable/animate/ziranchun/ziranchun0.plist", 
            "playing/chunTianTable/animate/ziranchun/ziranchun0.png"
        );
        ccs.armatureDataManager.addArmatureFileInfo(
            "playing/chunTianTable/animate/ziranchun/ziranchun.ExportJson"
        );
    },
    
    getJsBind: function(){
    return {
        _event: {
            endRoom: function(msg) {
                mylog(JSON.stringify(msg));
                if (msg.showEnd) this.addChild(new GameOverLayer(),500);
                else
                    MjClient.Scene.addChild(new StopRoomView());
                //选择飘分时候解散房间，清除飘分选择页面
                var layer = MjClient.playui.getChildByName("JiaZhu");
                if (layer) { layer.removeFromParent();}

                // 洗牌牌局未开始，解散房间需提示金额返还
                if (!msg.showEnd) {
                    return;
                }

                var mySelf = getUIPlayer(0);
                if (mySelf.shuffled && mySelf.shuffled > 0)
                    MjClient.showToast("因牌局解散，系统已返还洗牌费用");
                
            },
            roundEnd: function() {
                MjClient.selectTipCardsArray = null;

                var self = this;
                function delayExe()
                {
                    var sData = MjClient.data.sData;
                    var tData = sData.tData;

                    if(tData.tState && tData.tState !=TableState.roundFinish){
                        return 
                    }                        
                   
                    if (sData.tData.roundNum <= 0) {
                        if(!tData.matchId) {
                            self.addChild(new GameOverLayer(), 500);
                        }else {
                            self.runAction(cc.sequence(cc.delayTime(3),cc.callFunc(function(){
                                self.addChild(new GameOverLayer(),500);
                            })))
                        }
                    }

                    if (tData.tState && tData.tState ==TableState.roundFinish){
                        
                        if(self.getChildByTag(650)){
                            if(MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP || MjClient.getAppType() == MjClient.APP_TYPE.QXSYDTZ ){
                                var _lay = self.getChildByTag(650);
                                if(_lay){
                                    _lay.removeFromParent(true);
                                    _lay = null;
                                }
                            }
                        }

                        self.addChild(MjClient.playui.GetEndOneViewObj(),500, 650);
                    }
                }
                var _needShow = false;
                if( !MjClient.isInGoldFieldNormal() && !MjClient.isInGoldFieldQuick() 
                    && (MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP || 
                    MjClient.getAppType() == MjClient.APP_TYPE.QXSYDTZ) ){
                    _needShow = true;
                }

                // 金币场普通场需要展示其他玩家剩余手牌
                if ( (_needShow || MjClient.isInGoldFieldNormal()) && MjClient.rePlayVideo == -1) {
                    this.runAction(cc.sequence(cc.DelayTime(0.6), cc.callFunc(function(){
                        for (var off = 0; off <= 2; off++) {
                            MjClient.playui.cardLayoutRestore_endfiled(off);
                        }
                    })));
                }
                //金币场快速赛 清除牌桌已出的牌 并且添加得分动画
                if (MjClient.isInGoldFieldQuick() && MjClient.rePlayVideo == -1) {
                    MjClient.playui._btn_tuoguan.visible = false;
                    this.runAction(cc.sequence(cc.DelayTime(1), cc.callFunc(function(){
                        for (var off = 0; off <= 2; off++) {
                            MjClient.playui.cardLayoutRestore_endfiledQuick(off);
                        }
                        //添加动画
                    })));
                }

                var time = MjClient.isInGoldFieldNormal() ? 2 : 1;//金币场需要延迟2秒到小结算
                if(_needShow){
                    time = 2.1;
                }
                
                this.runAction(cc.sequence(cc.DelayTime(time),cc.callFunc(delayExe)));

                if(MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP || MjClient.getAppType() == MjClient.APP_TYPE.YLHUNANMJ)
                {
                    MjClient.playui.reConectHeadLayout_paodekuaiTY(this);
                }
            },
            moveHead: function() {
                postEvent("returnPlayerLayer");
                MjClient.playui._jiazhuWait.visible = false;

                // 跑得快不再播放头像移动动画
                //tableStartHeadMoveAction_card(this);
                
                MjClient.playui.reConectHeadLayout_paodekuaiTY(this);
                sendGPS();
                MjClient.checkChangeLocationApp();

                // 更新庄家标识
                setTimeout(function() {
                    MjClient.playui.updateAllZhuangIcons();
                }, 500);
            },
            initSceneData: function() {
                var self = this;
                if(MjClient.endoneui != null && cc.sys.isObjectValid(MjClient.endoneui))
                {
                    MjClient.endoneui.removeFromParent(true);
                }
                MjClient.endoneui = null;
                var sData = MjClient.data.sData;
                var tData = sData.tData;
                //self.removeChildByName("activeGoldPlaying");

                //初始化桌子的客户端数据
                MjClient.playui.InitC_Data();

                MjClient.playui.reConectHeadLayout_paodekuaiTY(this);
                MjClient.playui.showJiaZhu_Paodekuai();
                // MjClient.playui.resetJiaZhuNum(this);
                CheckRoomUiDelete();


                if(tData.tState == TableState.waitPut )
                {
                    MjClient.playui.UpdataCurrentPutCard();
                }

                for(var i = 0;i < MjClient.MaxPlayerNum;i++)
                {
                    currentLeftCardCount_paodekuai(i);
                }

                if(tData.tState <= TableState.waitReady)
                {
                    sendGPS();
                    MjClient.checkChangeLocationApp();
                }
                if (IsTurnToMe()) {
                    // 如果提示只有一手牌， 自动提起
                    // 如果提示只有一手牌， 且是我全部的手牌数量， 自动打出
                    AutoPutLastCard_card_ct();
                }
                //重新加载下操作按钮资源
                MjClient.playui.reLoadOptBtnRes();
                MjClient.playui.reLoadOptBtnRes2();

                // 更新庄家标识
                setTimeout(function() {
                    MjClient.playui.updateAllZhuangIcons();
                }, 500);
            },
            onlinePlayer: function(msg) {
                // MjClient.playui.reConectHeadLayout_paodekuaiTY(this);

                // 全局托管，自动准备移除小结算
                var mySelf = getUIPlayer(0);
                if (!mySelf)
                    return;

                if (!msg.isTrust){
                    return;
                }

                if (mySelf.info.uid != msg.uid)
                    return;

                postEvent("clearCardUI");

                if(MjClient.endoneui && cc.sys.isObjectValid(MjClient.endoneui)){
                    MjClient.endoneui.removeFromParent(true);
                    MjClient.endoneui = null;
                }

                if (MjClient.rePlayVideo >= 0 && MjClient.replayui && !MjClient.endallui) {
                    MjClient.replayui.replayEnd();
                }
               
                if (MjClient.arrowbkNode && cc.sys.isObjectValid( MjClient.arrowbkNode )) {
                    MjClient.arrowbkNode.setVisible(false);
                }
            },
            waitPut: function() {
                //重新加载下操作按钮资源
                MjClient.playui.reLoadOptBtnRes();
                MjClient.playui.reLoadOptBtnRes2();
            },
            waitBaoChun: function(msg) {
                cc.log("=== 客户端收到waitBaoChun事件 ===", JSON.stringify(msg));

                // 更新客户端的报春状态
                var tData = MjClient.data.sData.tData;
                if (msg.baoChunStatus) {
                    tData.baoChunStatus = msg.baoChunStatus;
                    cc.log("✅ 更新客户端报春状态:", tData.baoChunStatus);
                } else {
                    cc.log("⚠️ 服务端没有发送baoChunStatus");
                }

                cc.log("当前tData状态:", tData.baoChunStatus);
                cc.log("庄家索引:", tData.zhuang);
                cc.log("庄家UID:", tData.uids[tData.zhuang]);
                cc.log("自己UID:", SelfUid());
                MjClient.playui.startWaitBaoChun(msg);
            },
            waitZhuangConfirm: function(msg) {
                cc.log("进入waitZhuangConfirm",JSON.stringify(msg));
                MjClient.playui.startWaitZhuangConfirm(msg);
            },
            baoChunResult: function(msg) {
                cc.log("=== 客户端收到baoChunResult事件 ===", JSON.stringify(msg));
                cc.log("msg.isBaoChun:", msg.isBaoChun);
                cc.log("msg.zhuangDisagree:", msg.zhuangDisagree);
                cc.log("msg.baoChunPlayerName:", msg.baoChunPlayerName);
                MjClient.playui.startBaoChunResult(msg);

                // 报春结束后更新庄家标识
                setTimeout(function() {
                    MjClient.playui.updateAllZhuangIcons();
                }, 500);
            },
            chuntian: function(msg) {
                var UIoff = getUiOffByUid(msg.uid);
                cc.log("进入chuntian",JSON.stringify(msg));

                try {
                    var _armature = new ccs.Armature("chuntian");
                    if (!_armature) {
                        cc.log("❌ 春天动画创建失败");
                        return;
                    }

                    _armature.animation.play("chuntian");
                    var _node = getNode_cards(UIoff);
                    if (!_node) {
                        cc.log("❌ 找不到玩家节点，UIoff:", UIoff);
                        return;
                    }

                    var _nodeAni = _node.getChildByName("deskCard");
                    var posY = cc.winSize.height/2+100;
                    _armature.setPosition(cc.winSize.width/2, posY);
                    _armature.setScaleX(Math.abs(_nodeAni.getScale()*1.5));
                    _armature.setScaleY(_nodeAni.getScale()*1.5);
                    _node.addChild(_armature, 9999);
                    var seq = cc.sequence(
                        cc.delayTime(1),
                        cc.removeSelf()
                    );
                    _armature.runAction(seq);
                } catch (e) {
                }
            },
            ziranchun: function(msg) {
                var UIoff = getUiOffByUid(msg.uid);
                cc.log("进入ziranchun",JSON.stringify(msg));

                try {
                    var _armature = new ccs.Armature("ziranchun");
                    if (!_armature) {
                        cc.log("❌ 自然春动画创建失败");
                        return;
                    }

                    _armature.animation.play("ziranchun");
                    var _node = getNode_cards(UIoff);
                    if (!_node) {
                        cc.log("❌ 找不到玩家节点，UIoff:", UIoff);
                        return;
                    }

                    var _nodeAni = _node.getChildByName("deskCard");
                    var posY = cc.winSize.height/2+100;
                    _armature.setPosition(cc.winSize.width/2, posY);
                    _armature.setScaleX(Math.abs(_nodeAni.getScale()*1.5));
                    _armature.setScaleY(_nodeAni.getScale()*1.5);
                    _node.addChild(_armature, 9999);
                    var seq = cc.sequence(
                        cc.delayTime(1),
                        cc.removeSelf()
                    );
                    _armature.runAction(seq);
                } catch (e) {
                }
            },
            fanchun: function(msg) {
                var UIoff = getUiOffByUid(msg.uid);
                cc.log("进入fanchun",JSON.stringify(msg));

                try {
                    var _armature = new ccs.Armature("fanchun");
                    if (!_armature) {
                        cc.log("❌ 反春动画创建失败");
                        return;
                    }

                    _armature.animation.play("fanchun");
                    var _node = getNode_cards(UIoff);
                    if (!_node) {
                        cc.log("❌ 找不到玩家节点，UIoff:", UIoff);
                        return;
                    }

                    var _nodeAni = _node.getChildByName("deskCard");
                    var posY = cc.winSize.height/2+100;
                    _armature.setPosition(cc.winSize.width/2, posY);
                    _armature.setScaleX(Math.abs(_nodeAni.getScale()*1.5));
                    _armature.setScaleY(_nodeAni.getScale()*1.5);
                    _node.addChild(_armature, 9999);
                    var seq = cc.sequence(
                        cc.delayTime(1),
                        cc.removeSelf()
                    );
                    _armature.runAction(seq);
                } catch (e) {
                }
            },
            shachun: function(msg) {
                var UIoff = getUiOffByUid(msg.uid);
                cc.log("进入shachun",JSON.stringify(msg));

                try {
                    var _armature = new ccs.Armature("shachun");
                    if (!_armature) {
                        cc.log("❌ 杀春动画创建失败");
                        return;
                    }

                    _armature.animation.play("shachun");
                    var _node = getNode_cards(UIoff);
                    if (!_node) {
                        cc.log("❌ 找不到玩家节点，UIoff:", UIoff);
                        return;
                    }

                    var _nodeAni = _node.getChildByName("deskCard");
                    var posY = cc.winSize.height/2+100;
                    _armature.setPosition(cc.winSize.width/2, posY);
                    _armature.setScaleX(Math.abs(_nodeAni.getScale()*1.5));
                    _armature.setScaleY(_nodeAni.getScale()*1.5);
                    _node.addChild(_armature, 9999);
                    var seq = cc.sequence(
                        cc.delayTime(1),
                        cc.removeSelf()
                    );
                    _armature.runAction(seq);
                } catch (e) {
                }
            },
            putCardError: function(msg) {
                cc.log("=== 收到出牌错误提示 ===", JSON.stringify(msg));

                // 显示错误提示
                if (msg.message) {
                    MjClient.showToast(msg.message);
                }

                // 如果是报单必打错误，可以添加特殊处理
                if (msg.error === 'baodanbida_must_max_single') {
                    cc.log("报单必打：必须出最大单牌");
                    // 可以在这里添加特殊的UI提示或者自动选择最大单牌
                }
            },
        },
        gameName:{
            _run:function()
            {
                this.loadTexture(GameBg[MjClient.gameType]);
                cc.log("666666666"+MjClient.gameType);
                setWgtLayout(this,[0.12, 0.06],[0.5, 0.6],[0, 0]);
            }
        },
        roundInfo:{
            _visible: false,
        },
        jiazhuWait: {
            _visible: false,
            _layout: [
                [0.2, 0.2],
                [0.5, 0.7],
                [0, 0]
            ]
        },
        banner: {
            _layout: [
                [1, 0.5],
                [0.5, 1],
                [0, 0]
            ],
            // 云南需求跑得快1:1调整
            btn_menu: {
                _visible: true,
                _click: function(btn,et){
                    var bg_menu = btn.getChildByName("Panel_menu");
                    if (bg_menu.visible) {
                        bg_menu.visible = false;
                    } else {
                        bg_menu.visible = true;
                        // bg_menu.runAction(cc.sequence(
                        //     cc.scaleTo(3, 1).easing(cc.easeBackIn()),
                        //     cc.callFunc(function() {
                        //         bg_menu.visible = false;
                        //     })
                        // ));
                    }
                },
                Panel_menu: {
                    _visible: false,
                    btn_rule: {
                        _click: function() {
                            var settingLayer = new GameRuleLayer();
                            MjClient.Scene.addChild(settingLayer);
                        }
                    },
                    btn_set: {
                        _click: function() {
                            var settingLayer = new SettingView();
                            settingLayer.setName("PlayLayerClick");
                            MjClient.Scene.addChild(settingLayer);
                        }
                    },
                    btn_gps: {
                        _run: function() {
                            this.setVisible((MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP|| MjClient.getAppType() == MjClient.APP_TYPE.YLHUNANMJ) && MjClient.MaxPlayerNum != 2);
        
                            // var banner = this.parent;
                            // var waitNode = MjClient.playui.getChildByName("playUINode").getChildByName("wait");
                            // var delroom = waitNode.getChildByName("delroom");
                            // var backHomebtn = waitNode.getChildByName("backHomebtn");
                            // var distanceX = banner.getChildByName("setting").getPositionX() - banner.getChildByName("changeBg").getPositionX();
        
                            // delroom.setScale(banner.scaleX,banner.scaleY);
                            // backHomebtn.setScale(banner.scaleX,banner.scaleY);
        
                            // if (this.isVisible()) {
                            //     delroom.setPosition(waitNode.convertToNodeSpace(banner.convertToWorldSpace(cc.p(this.getPositionX() - distanceX,this.getPositionY()))))
                            //     backHomebtn.setPosition(waitNode.convertToNodeSpace(banner.convertToWorldSpace(cc.p(this.getPositionX() - 2*distanceX,this.getPositionY()))))
                            // }
                            // else {
                            //     delroom.setPosition(waitNode.convertToNodeSpace(banner.convertToWorldSpace(this.getPosition())))
                            //     backHomebtn.setPosition(waitNode.convertToNodeSpace(banner.convertToWorldSpace(cc.p(this.getPositionX() - distanceX,this.getPositionY()))))
                            // }
                        },
                        _click: function() {
                            if (MjClient.MaxPlayerNum == 3) {
                                MjClient.Scene.addChild(new showDistance3PlayerLayer());
                            } else if (MjClient.MaxPlayerNum == 4) {
                                MjClient.Scene.addChild(new showDistanceLayer());
                            }
                            
                        }
                    },
                    btn_exit: {
                        _run: function(btn) {
                            var btn_gps = this.getParent().getChildByName("btn_gps");
                            if (btn_gps.visible == false) {
                                this.setPosition(btn_gps.getPosition());
                            }
                        },
                        _click: function() {
                            MjClient.delRoom(true);
                        },
                    },
                }
            },
            info_bg: {
                tableid_img: {
                    tableid_0: {
                        _event: {
                            initSceneData: function() {
                                this.ignoreContentAdaptWithSize(true);
                                this.setString(MjClient.data.sData.tData.tableid);
                            }
                        }
                    },
                },
                roundnumAtlas_img: {
                    roundnumAtlas_0: {_text: function() {
                        var sData = MjClient.data.sData;
                        var tData = sData.tData;
                        var extraNum = tData.extraNum ? tData.extraNum:0;
                        if (tData) return (tData.roundAll-tData.roundNum + 1 + extraNum) + "/" + tData.roundAll;
                    },
                    _event: {
                        mjhand: function() {
                            var sData = MjClient.data.sData;
                            var tData = sData.tData;
                            var extraNum = tData.extraNum ? tData.extraNum:0;
                            if (tData) return this.setString((tData.roundAll-tData.roundNum + 1 + extraNum) + "/" + tData.roundAll);
                        },
                        initSceneData: function() {
                            var sData = MjClient.data.sData;
                            var tData = sData.tData;
                            var extraNum = tData.extraNum ? tData.extraNum:0;
                            if (tData) return this.setString((tData.roundAll-tData.roundNum + 1 + extraNum) + "/" + tData.roundAll);
                        },
                    }
                    },
                },
                txt_time:{
                     _run:function()
                    {
                        var text = new ccui.Text();
                        text.setFontName("fonts/fzcy.TTF");
                        text.setFontSize(26);
                        text.setTextColor(cc.color("#DEE2C7"));
                        text.setAnchorPoint(0.5,0.5);
                        text.setPosition(this.getContentSize().width / 2, this.getContentSize().height / 2);
                        this.addChild(text);
                        text.schedule(function(){

                            var time = MjClient.getCurrentTime();
                            var str = (time[3]<10?"0"+time[3]:time[3])+":"+
                                (time[4]<10?"0"+time[4]:time[4]);
                            this.setString(str);
                        });
                    }
                },
                powerBar: {
                    _run: function() {
                        //cc.log("powerBar_run");
                        updateBattery(this);
                    },
                    _event: {
                        nativePower: function(d) {
                            this.setPercent(Number(d));
                        }
                    }
                },
                wifi: {
                    _run: function() {
                        updateWifiState_new(this);
                    }
                },
            },
            tableid: {
                _run: function() {
                    this.visible = false;
                },
                // _event: {
                //     initSceneData: function() {
                //         this.ignoreContentAdaptWithSize(true);
                //         this.setString("房间号：" + MjClient.data.sData.tData.tableid);
                //     }
                // }
            },
            roundnumAtlas:{
                _run: function() {
                   this.visible = false; 
                },
                _text: function() {
                    var sData = MjClient.data.sData;
                    var tData = sData.tData;
                    var extraNum = tData.extraNum ? tData.extraNum:0;
                    if (tData) return "局数：" + (tData.roundAll-tData.roundNum + 1 + extraNum) + "/" + tData.roundAll;
                },
                _event: {
                    mjhand: function() {
                        var sData = MjClient.data.sData;
                        var tData = sData.tData;
                        var extraNum = tData.extraNum ? tData.extraNum:0;
                        if (tData) return this.setString("局数：" + (tData.roundAll-tData.roundNum + 1 + extraNum) + "/" + tData.roundAll);
                    },
                    initSceneData: function() {
                        var sData = MjClient.data.sData;
                        var tData = sData.tData;
                        var extraNum = tData.extraNum ? tData.extraNum:0;
                        if (tData) return this.setString("局数：" + (tData.roundAll-tData.roundNum + 1 + extraNum) + "/" + tData.roundAll);
                    },
                }
            },
            setting: {
                _run: function() {
                    this.visible = false;
                },
                // _click: function() {
                //     var settingLayer = new SettingView();
                //     settingLayer.setName("PlayLayerClick");
                //     MjClient.Scene.addChild(settingLayer);
                // }
            },
            gps_btn: {
                _run: function() {
                    this.visible = false;
                    // this.setVisible(MjClient.MaxPlayerNum != 2);
                }
            },
            btn_gameRule: {
                _run: function() {
                    this.visible = false;
                },
                // _click: function() {
                //     var settingLayer = new GameRuleLayer();
                //     MjClient.Scene.addChild(settingLayer);
                // }
            }
        },
        wait: {
            _run:function() {
                this.visible = true;
            },
            getRoomNum: {
                _run:function(){
                    setWgtLayout(this, [0.15, 0.15],[0.3, 0.3],[0, 0]);
                },
                _visible:function()
                {
                    if( MjClient.isInGoldField() )return false;
                    return !MjClient.remoteCfg.guestLogin;
                },
                _click: function() {
                    getPlayingRoomInfo(1);
                }
            },
            wxinvite: {
                _layout: [[0.16, 0.16],[0.5, 0.43],[0, 0]],
                _click: function() {
                    
                    getPlayingRoomInfo(2);
                },
                _visible:function()
                {
                    if (MjClient.isInGoldField()) return false;
                    return !MjClient.remoteCfg.guestLogin;
                }
            },

            backHomebtn: {
                _run:function(){
                    if (isIPhoneX()) {
                        setWgtLayout(this, [0.15, 0.15],[0.7, 0.3],[0, 0]);
                    }
                    else {
                        setWgtLayout(this, [0.15, 0.15], [0.7, 0.3], [0, 0]);
                    }
                },
                _visible:function()
                {
                    if (MjClient.isInGoldField()) return false;
                    return !MjClient.remoteCfg.guestLogin;
                },
                _click: function(btn) {
                    var sData = MjClient.data.sData;
                    if (sData) {
                        // 使用标准的返回大厅功能
                        onClickBackHallBtn();
                    }
                },
                _event: {
                    waitReady: function() {
                        // 在等待准备状态时，如果所有玩家都准备了就隐藏按钮
                        this.visible = false;
                    },
                    initSceneData: function(eD) {
                        var isWaitReady = eD.tData.tState == TableState.waitReady;
                        this.visible = IsInviteVisible() && (!isWaitReady || !IsAllPlayerReadyState());
                    },
                    addPlayer: function(eD) {
                        var isWaitReady = eD.tData.tState == TableState.waitReady;
                        this.visible = IsInviteVisible() && (!isWaitReady || !IsAllPlayerReadyState());
                    },
                    removePlayer: function(eD) {
                        var isWaitReady = eD.tData.tState == TableState.waitReady;
                        this.visible = IsInviteVisible() && (!isWaitReady || !IsAllPlayerReadyState());
                    }
                }
            },

            exitRoomBtn: {
                _run:function(){
                    if (isIPhoneX()) {
                        setWgtLayout(this, [0.15, 0.15],[0.5, 0.3],[0, 0]);
                    }
                    else {
                        setWgtLayout(this, [0.15, 0.15], [0.5, 0.3], [0, 0]);
                    }
                },
                _visible:function()
                {
                    if (MjClient.isInGoldField()) return false;
                    return !MjClient.remoteCfg.guestLogin;
                },
                _click: function(btn) {
                    // 退出房间 - 玩家彻底离开房间
                    var sData = MjClient.data.sData;
                    if (sData) {
                        if (IsRoomCreator()) {
                            MjClient.showMsg("确定要解散房间吗？\n解散后房间将被删除",
                                function() {
                                    MjClient.delRoom(true);
                                },
                                function() {});
                        } else {
                            MjClient.showMsg("确定要退出房间吗？\n退出后将离开当前房间",
                                function() {
                                    MjClient.leaveGame();
                                },
                                function() {});
                        }
                    }
                },
                _event: {
                    waitReady: function() {
                        // 退出房间按钮在等待准备状态时始终可见
                        this.visible = IsInviteVisible();
                    },
                    initSceneData: function(eD) {
                        this.visible = IsInviteVisible();
                    },
                    addPlayer: function(eD) {
                        this.visible = IsInviteVisible();
                    },
                    removePlayer: function(eD) {
                        this.visible = IsInviteVisible();
                    }
                }
            },
            _event: {
                // onlinePlayer: function() {
                //     if( IsAllPlayerReadyState() ) {
                //         this.getChildByName('delroom').visible = false;
                //         this.getChildByName('backHomebtn').visible = false;
                //     } 
                // },
                initSceneData: function(eD) {
                    var isWaitReady = eD.tData.tState == TableState.waitReady;
                    this.getChildByName('getRoomNum').visible = IsInviteVisible();//IsInviteVisible();
                    this.getChildByName('wxinvite').visible = IsInviteVisible() && !MjClient.remoteCfg.guestLogin;
                    this.getChildByName('delroom').visible = IsInviteVisible() || isWaitReady;
                    // backHomebtn 和 exitRoomBtn 的显示逻辑由各自的事件处理
                },
                addPlayer: function(eD) {
                    console.log(">>>>>> play add player >>>>");
                    this.getChildByName('getRoomNum').visible =  IsInviteVisible() ;//IsInviteVisible();
                    this.getChildByName('wxinvite').visible = IsInviteVisible() && !MjClient.remoteCfg.guestLogin;
                    // backHomebtn 和 exitRoomBtn 的显示逻辑由各自的事件处理
                },
                removePlayer: function(eD) {
                    this.getChildByName('getRoomNum').visible =  IsInviteVisible() ;//IsInviteVisible();
                    this.getChildByName('wxinvite').visible = !MjClient.remoteCfg.guestLogin;
                    // backHomebtn 和 exitRoomBtn 的显示逻辑由各自的事件处理
                }
            }
        },
        BtnHimt:{ //add by  sking for put card button
            _run: function () {
                this.visible = false;
                if (MjClient.data.sData.tData.areaSelectMode.mustPut)
                {
                    if(isIPhoneX())
                        setWgtLayout(this,[0.15, 0.14], [0.52, 0.46], [-0.8, 0.26]);
                    else
                        setWgtLayout(this,[0.15, 0.14], [0.52, 0.39], [-0.8, 0.26]);
                }
                else
                {
                    if(isIPhoneX())
                        setWgtLayout(this,[0.15, 0.14], [0.52, 0.46], [0, 0.26]);
                    else
                        setWgtLayout(this,[0.15, 0.14], [0.52, 0.39], [0, 0.26]);
                }
            },
        },//end of add by sking
        isbaochun:{
         btn_baoChun: {
            _visible: false,
            _run: function () {
                setWgtLayout(this,[0.15, 0.14], [0.52, 0.5], [-0.8, 0.26]);
                this.visible = false;
            },
        },
        btn_buBaoChun: {
            _visible: false,
            _run: function () {
                setWgtLayout(this, [0.15, 0.14], [0.5, 0.5], [0.5, 0.26]);
                this.visible = false;
                if (MjClient.data.sData.tData.areaSelectMode.mustPut){
                    setWgtLayout(this, [0.15, 0.14], [0.5, 0.5], [0.8, 0.26]);
                }
            },
        },
    },
        BtnReady: {
            _visible: false,
            _run: function() {
                if (MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP || MjClient.getAppType() == MjClient.APP_TYPE.YLHUNANMJ) {
                    setWgtLayout(this,[0.16, 0], [0.5, 0.4], [0, 0]);
                }
                else {
                    setWgtLayout(this,[0.2, 0.2], [0.5, 0.4], [0, 0]);
                }  
            },
            _click: function(_this) {
                PKPassConfirmToServer_card();
                
            },
            _event: {
                waitReady: function() {
                    this.visible = true;
                },
                mjhand: function() {
                    this.visible = false;
                },
                initSceneData: function() {
                    this.visible = false;
                    var tData = MjClient.data.sData.tData;
                    var pl = getUIPlayer(0);
                    this.visible = tData.roundNum == tData.roundAll && tData.tState == TableState.waitReady && pl.mjState == TableState.waitReady && !IsInviteVisible();
                },
                PKPass: function() {
                    this.visible = false;
                },
                removePlayer: function(eD) {
                    this.visible = false;
                },
                onlinePlayer: function(msg) {
                    if (msg.uid == SelfUid()) {
                        this.visible = false;
                    }
                },
            },
        },
        BtnNoPut:{
            _visible: false,
            _run: function () {
                this.visible = false;
                this.setTouchEnabled(false);
                if(isIPhoneX())
                    setWgtLayout(this,[0.15, 0.14], [0.525, 0.46], [-1.3, 0.26]);
                else
                    setWgtLayout(this,[0.15, 0.14], [0.525, 0.39], [-1.3, 0.26]);
            },
        },
        BtnPutCard:{
            _run: function () {
                this.visible = false;
                if (MjClient.data.sData.tData.areaSelectMode.mustPut)
                {
                    if(isIPhoneX())
                        setWgtLayout(this,[0.15, 0.14], [0.5, 0.46], [0.8, 0.32]);
                    else
                        setWgtLayout(this,[0.15, 0.14], [0.5, 0.39], [0.8, 0.32]);
                }          
                else
                {
                    if(isIPhoneX())
                        setWgtLayout(this,[0.15, 0.14], [0.515, 0.46], [1.3, 0.32]);
                    else
                        setWgtLayout(this,[0.15, 0.14], [0.515, 0.39], [1.3, 0.32]); 
                }
            },
        },//end of add by skin
        baoDanMaxTip: {
            _run: function() {
                this.visible = false;
                setWgtLayout(this, [0.56, 0], [0.5, 0.1], [0, 0]);

                this.checkVisible = function() {
                    var tData = MjClient.data.sData.tData;
                    var plNext = getUIPlayer(1);

                    // 下家报单,单牌请出最大牌 - 只要下家报单就显示提示
                    if (IsTurnToMe() && tData.tState == TableState.waitPut && plNext && plNext.handCount == 1) {
                        this.visible = true; // 只要下家报单就显示提示，不再限制其他条件
                    } else {
                        this.visible = false;
                    }
                }
            },
            _event: {
                clearCardUI: function() {
                    this.visible = false;
                },
                mjhand: function() {
                    this.visible = false;
                },
                PKPut: function(eD) {
                    this.visible = false;
                },
                initSceneData: function(eD) {
                    this.checkVisible();
                },
                waitPut: function() {
                    this.checkVisible();
                },
            }
        },
        flyCard:{
            _event:{
                waitPut:function(eD){
                    var tData = MjClient.data.sData.tData;
                    if (tData.roundNum == tData.roundAll && tData.lastPutPlayer == -1) {
                        setWgtLayout(this,[0.036, 0], [0.5, 0.75], [0, 0]);
                        MjClient.playui.shwoFlyCardAnim(this);
                    }
                }
            }
        },
        down: {
            head: {
                jiaZhu: {
                    _run: function() {
                        this.visible = false;
                    },
                    _event: {
                        clearCardUI: function(eD) {
                            this.visible = false;
                        }
                    }
                },
                head_bottom:{_visible:false},
                name_bottom:{_visible:false},
                icon_bottom:{_visible:false},
                zhuang: {
                    _event: {
                        initSceneData: function() {
                            MjClient.playui.showZhuangIcon(0); // down位置是索引0
                        },
                        moveHead: function() {
                            MjClient.playui.showZhuangIcon(0);
                        },
                        addPlayer: function() {
                            MjClient.playui.showZhuangIcon(0);
                        },
                        removePlayer: function() {
                            MjClient.playui.showZhuangIcon(0);
                        }
                    }
                },
            },
            ready: {
                _run: function() {
                    setWgtLayout(this, [this.width / 1280, this.height / 720], [0.5, 0.5], [0, -1.5]);
                    GetReadyVisible(this, 0);
                    //this.visible = true;
                },
            },
            _event: {
                updateInfo:function (d) {
                    if(MjClient.isInGoldField() && d && d.gold){
                        InitUserCoinAndName(this,0);
                    }
                },
                PKPut: function(eD) {
                    //cc.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>PKPut---------------");
                    var pl = getUIPlayer(0);
                    if(pl && pl.trust || eD.uid != SelfUid() ||  MjClient.rePlayVideo != -1)
                        DealMJPut_card(this,eD, 0);

                    setUserOffline(this, 0);

                    // 解决异常情况下，手牌结点没有移除
                    correctPKPut(eD, 0);
                },
                waitPut:function(eD){
                    cc.log(">>>>>>>>>>>>>>>>down>>>>>>>>>>>>>>>waitPut");

                    var tData = MjClient.data.sData.tData;
                    if (MjClient.playui.isShowHandCardBeiMain && (tData.curPlayer == getPlayerIndex(0) || tData.lastPutPlayer != -1)) {
                        MjClient.playui.isShowHandCardBeiMain = false;
                        MjClient.playui.hideHandCardBeiMian();
                    }

                    // 发牌时暂时不计算牌型提示
                    if (!MjClient.playui.isFaPai)
                    {
                        if (MjClient.playui.isWaitAniEnd)
                        {
                            delete MjClient.playui.isWaitAniEnd;
                            MjClient.playui.showOffenseAnim();
                        }

                        DealWaitPut_card(this, eD, 0);
                        UpdataCurrentPutCard();
                        // 跑得快 自动出牌
                        if (IsTurnToMe()) {
                            // 如果提示只有一手牌， 自动提起
                            // 如果提示只有一手牌， 且是我全部的手牌数量， 自动打出
                            AutoPutLastCard_card_ct();
                        }                           
                    }
                },
                PostCardsEnded: function()
                {
                    // 发牌完毕处理正常牌逻辑
                    if(!MjClient.playui.isFaPai && 
                        MjClient.data.sData.tData.tState == TableState.waitPut && 
                        MjClient.playui.isWaitAniEnd)
                    {
                        delete MjClient.playui.isWaitAniEnd;
                        MjClient.playui.showOffenseAnim();

                        DealWaitPut_card(this, null, 0);
                        UpdataCurrentPutCard();
                        // 跑得快 自动出牌
                        if (IsTurnToMe()) {
                            // 如果提示只有一手牌， 自动提起
                            // 如果提示只有一手牌， 且是我全部的手牌数量， 自动打出
                            AutoPutLastCard_card_ct();
                        }
                    }
                },
                onlinePlayer: function(eD) {
                    setUserOffline(this, 0);
                },
                playerStatusChange: function(eD) {
                    setUserOffline(this, 0);
                },
                waitJiazhu: function(msg) {
                    var tData = MjClient.data.sData.tData;
                    var layer = new JiaZhu_PaodekuaiTY(tData.areaSelectMode.piaofen,function(){
                        //弹窗等待
                        MjClient.playui._jiazhuWait.visible = true;
                    });
                    layer.setName("JiaZhu");
                    cc.log("_jiazhuWait===================2222");
                    MjClient.playui.getChildByName("playUINode").addChild(layer, 99);
                },
            }
        },
        right: {
            head: {
                jiaZhu: {
                    _run: function() {
                        this.visible = false;
                    },
                    _event: {
                        clearCardUI: function(eD) {
                            this.visible = false;
                        }
                    }
                },
                head_bottom:{_visible:false},
                name_bottom:{_visible:false},
                icon_bottom:{_visible:false},
                tingCard:{
                    _run: function () {
                        var tData = MjClient.data.sData.tData;
                        if(MjClient.isInGoldField()) this.y = -10;
                        else if (isIPhoneX() && MjClient.rePlayVideo != -1)
                            this.setPositionX(-24);
                    },
                },
                zhuang: {
                    _event: {
                        initSceneData: function() {
                            MjClient.playui.showZhuangIcon(1); // right位置是索引1
                        },
                        moveHead: function() {
                            MjClient.playui.showZhuangIcon(1);
                        },
                        addPlayer: function() {
                            MjClient.playui.showZhuangIcon(1);
                        },
                        removePlayer: function() {
                            MjClient.playui.showZhuangIcon(1);
                        }
                    }
                },

            },
            ready: {
                _run: function() {
                    setWgtLayout(this, [this.width / 1280, this.height / 720], [0.5, 0.5], [2, 0]);
                    GetReadyVisible(this, 1);
                },
            },
            stand: {
                _run: function() {
                    if (isIPhoneX() && !MjClient.isInGoldField())
                        setWgtLayout(this,[0, 0.13],[1, 1],[-2.1, 0]);
                    else
                        setWgtLayout(this,[0, 0.13],[1, 1],[-2.5, 0]);

                    this.visible = false;
                }
            },
            deskCard: {
                // _layout: [
                //     [0.1, 0.15],
                //     [1, 0.55],
                //     [-3, 0]
                // ],
                _run:function()
                {
                    if(MjClient.rePlayVideo == -1)// 表示正常游戏
                        setWgtLayout(this,[0.052, 0],[1, 0.65],[-3.5, 0.5]);
                    else if (isIPhoneX() && MjClient.rePlayVideo != -1 && !MjClient.isInGoldField())
                        setWgtLayout(this,[0.052, 0],[1, 0.65],[-4.25, 0.5]);
                    else
                        setWgtLayout(this,[0.052, 0],[1, 0.65],[-4.2, 0.5]);
                },
            },
            noPutTag: {
                _run:function()
                {
                    var tData = MjClient.data.sData.tData;
                    var pos = MjClient.playui._rightNode.getChildByName("deskCard").getPosition();
                    if (MjClient.isInGoldField())pos.y -= 20;
                    this.setScale(MjClient.size.width/1280);
                    this.setPosition(pos);
                },
            },
        },
        top: {
            head: {
                jiaZhu: {
                    _run: function() {
                        this.visible = false;
                    },
                    _event: {
                        clearCardUI: function(eD) {
                            this.visible = false;
                        }
                    }
                },
                head_bottom:{_visible:false},
                name_bottom:{_visible:false},
                icon_bottom:{_visible:false},
                tingCard:{
                    _run: function () {
                        var tData = MjClient.data.sData.tData;
                        if(MjClient.isInGoldField()) this.y = -10;
                        else if (isIPhoneX() && MjClient.rePlayVideo != -1)
                            this.setPositionX(151);
                    },
                },
                zhuang: {
                    _event: {
                        initSceneData: function() {
                            MjClient.playui.showZhuangIcon(2); // top位置是索引2
                        },
                        moveHead: function() {
                            MjClient.playui.showZhuangIcon(2);
                        },
                        addPlayer: function() {
                            MjClient.playui.showZhuangIcon(2);
                        },
                        removePlayer: function() {
                            MjClient.playui.showZhuangIcon(2);
                        }
                    }
                },
            },
            ready: {
                _run: function() {
                    setWgtLayout(this, [this.width / 1280, this.height / 720], [0.5, 0.5], [-2, 0]);
                    GetReadyVisible(this, 2);
                },
            },
            stand: {
                _run: function() {
                    if (isIPhoneX() && !MjClient.isInGoldField())
                        setWgtLayout(this,[0, 0.13],[0, 1],[3.15, 0]);
                    else
                        setWgtLayout(this,[0, 0.13],[0, 1],[2.5, 0]);

                    this.visible = false;
                }
            },
            deskCard: {
                // _layout: [
                //     [0.12, 0.15],
                //     [0.16, 0.55],
                //     [0, 0.1]
                // ],
                _run:function()
                {
                    if(MjClient.rePlayVideo == -1)// 表示正常游戏
                        setWgtLayout(this,[0.052, 0],[0.16, 0.65],[0.5, 0.5]);
                    else if (isIPhoneX() && MjClient.rePlayVideo != -1 && !MjClient.isInGoldField())
                        setWgtLayout(this,[0.052, 0],[0.16, 0.65],[1.6, 0.5]);
                    else
                        setWgtLayout(this,[0.052, 0],[0.16, 0.65],[1.2, 0.5]);
                },
            },
            noPutTag: {
                _run:function()
                {
                    var tData = MjClient.data.sData.tData;
                    var pos = MjClient.playui._topNode.getChildByName("deskCard").getPosition();
                    if (MjClient.isInGoldField())pos.y -= 20;
                    this.setScale(MjClient.size.width/1280);
                    this.setPosition(pos);
                },
            },
        },
        clock: {
            _run:function () {
                if(MjClient.data.sData.tData.areaSelectMode.mustPut == true)
                {
                    if(isIPhoneX())
                        setWgtLayout(this,[0.065, 0.14],[0.25,0.49],[0,0]);
                    else
                        setWgtLayout(this,[0.065, 0.14],[0.25,0.42],[0,0]);
                }
                else
                {
                    if(isIPhoneX())
                        setWgtLayout(this,[0.065, 0.14],[0.17,0.49],[0,0]);
                    else
                        setWgtLayout(this,[0.065, 0.14],[0.17,0.42],[0,0]);
                }
                
                MjClient.clockNode = this;
                this.visible = false;
                this.zIndex = 100;
                this.srcPosition = this.getPosition();
            },
            number: {
                _event: {
                    initSceneData: function() {
                        if (MjClient.data.sData.tData.tState == TableState.waitPut)
                        {
                            MjClient.clockNode.visible = true;
                            if ((MjClient.isInGoldField()) && !MjClient.data.sData.tData.lastPutCard){
                                stopEffect(playTimeUpEff);
                                MjClient.playui.clockNumberUpdate(this);
                            }else{
                                this.setString("0");
                            }

                            MjClient.playui.updateClockPosition(MjClient.clockNode);
                        }
                    },
                    onlinePlayer: function() {
                        if (!MjClient.isInGoldField()){
                            MjClient.clockNode.visible = false;
                        }
                    },
                }
            },
        },
        chat_btn: {
            _run: function() {
                setWgtLayout(this, [0.1, 0.1], [0.965, 0.22], [0, 3.5]); 
            },
        },
        voice_btn: {
            _run: function() {
                setWgtLayout(this, [0.1, 0.1], [0.965, 0.1], [0, 3.5]);
                // this.visible = false;
                initVoiceData();
                cc.eventManager.addListener(getTouchListener(), this);
            },
            _touch: function(btn, eT) {
                // 点击开始录音 松开结束录音,并且上传至服务器, 然后通知其他客户端去接受录音消息, 播放
                if (eT == 0) {
                    startRecord();
                } else if (eT == 2) {
                    endRecord();
                } else if (eT == 3) {
                    cancelRecord();
                }
            },
            _event: {
                cancelRecord: function() {
                    MjClient.native.HelloOC("cancelRecord !!!");
                },
                uploadRecord: function(filePath) {
                    if (filePath) {
                        MjClient.native.HelloOC("upload voice file");
                        MjClient.native.UploadFile(filePath, MjClient.remoteCfg.voiceUrl, "sendVoice");
                    } else {
                        MjClient.native.HelloOC("No voice file update");
                    }
                },
                sendVoice: function(fullFilePath) {
                    if (!fullFilePath) {
                        console.log("sendVoice No fileName");
                        return;
                    }

                    var getFileName = /[^\/]+$/;
                    var extensionName = getFileName.exec(fullFilePath);
                    var fileName = extensionName[extensionName.length - 1];
                    console.log("sfileName is:" + fileName);

                    MjClient.gamenet.request("pkroom.handler.tableMsg", {
                        cmd: "downAndPlayVoice",
                        uid: SelfUid(),
                        type: 3,
                        msg: fileName,
                        num: MjClient.data._JiaheTempTime//录音时长
                    });
                    MjClient.native.HelloOC("download file");
                },
                downAndPlayVoice: function(msg) {
                    MjClient.native.HelloOC("downloadPlayVoice ok");
                    MjClient.data._tempMessage = msg;
                    MjClient.native.HelloOC("mas is" + JSON.stringify(msg));
                    downAndPlayVoice(msg.uid, msg.msg);
                }
            }
        },
        block_tuoguan:{
            Text_1:{
                _run: function() {
                    this.ignoreContentAdaptWithSize(true);
                },
            },
        }
    };
},
ctor: function(json) {
    this.preloadAnimations();
    var resJson = json || res.Play_ChunTian_json;
    var playui = this._super(resJson);

    MjClient.playui._btn_tuoguan =  playui.node.getChildByName("block_tuoguan");

    //金币场进来加载先用临时图遮挡ui
    // this.bg_temp = playui.node.getChildByName("bg_temp");
    // this.top_banner = playui.node.getChildByName("banner");
    // if (this.bg_temp){
    //     if(MjClient.rePlayVideo == -1 && MjClient.data && MjClient.data.sData && MjClient.data.sData.tData && MjClient.isInGoldField()){
    //         this.bg_temp.visible = true;
    //         this.top_banner.zIndex = 1000;
    //         this.bg_temp.zIndex = 1000;
    //         this.runAction(cc.sequence(cc.delayTime(0.5),cc.callFunc(function() {
    //             MjClient.playui.bg_temp.zIndex = -1;
    //             MjClient.playui.top_banner.zIndex = 0;
    //             MjClient.playui.bg_temp.visible = false;
    //         })));
    //     }else{
    //         this.bg_temp.zIndex = -1;
    //         this.bg_temp.visible = false;
    //     }
    // }

    // 在亲友圈房间中添加邀请亲友圈牌有一起对局
    addClubYaoqingBtn(1);
    // addClub_BackHallBtn(true);
    // 俱乐部返回大厅功能：by_jcw
    // addClub_BackHallBtn(true);

    // 回看
    if(MjClient.rePlayVideo == -1 && (MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP|| MjClient.getAppType() == MjClient.APP_TYPE.YLHUNANMJ) && !MjClient.isInGoldField()){
        this._viewLookBack = COMMON_UI.createPokerLookbackView()
        playui.node.addChild(this._viewLookBack);
        this._viewLookBack.setPosition(MjClient.size.width / 2,MjClient.size.height / 2);
        this._viewLookBack.setContentSize(MjClient.size.width, MjClient.size.height);
    }
    return playui;
}

});

PlayPanel_chuntian.prototype.GetEndOneViewObj = function()
{
    return new EndOneView_PaoDeKuaiTY();
}

// add...................   Greene
//初始化桌子上的客户端数据c_data..各个游戏的自身数据可以在这里初始，
//尽量不要在外部公共代码判断游戏类型，而是在c_data里初始化数据。
//在PlayLayer的_event 的 initSceneData调用
PlayPanel_chuntian.prototype.InitC_Data = function() {
    if (!MjClient.data.c_Data)
        MjClient.data.c_Data = {};
    cc.log("InitC_Data===========================")
    //出牌是否动画
    MjClient.data.c_Data.bPutCardAnim =  MjClient.data.sData.tData.areaSelectMode.playSpeed != 0;
    //是发采用老的牌型动画
    MjClient.data.c_Data.bPutCardAnimOld = MjClient.data.sData.tData.areaSelectMode.playSpeed == 0;
    //牌型动画是否是文字图片
    MjClient.data.c_Data.bTxtAnim = MjClient.data.sData.tData.areaSelectMode.playSpeed == 0;
}

// off 是四个位置，根据off 显示四个位置的信息 by sking
PlayPanel_chuntian.prototype.setUserVisiblePaoDeKuai = function (node, off)
{
    var pl = getUIPlayer(off);
    var head = node.getChildByName("head");
    var name = head.getChildByName("name");
    var nobody = head.getChildByName("nobody");
    var coin = head.getChildByName("coin");
    var offline = head.getChildByName("offline");
    var name_bg = head.getChildByName("name_bg");
    var score_bg = head.getChildByName("score_bg");
    var head_bottom = head.getChildByName("head_bottom");
    var name_bottom = head.getChildByName("name_bottom");
    var icon_bottom = head.getChildByName("icon_bottom");

    if(pl)
    {
        name.visible = true;
        coin.visible = true;
        offline.visible = true;
        coin.visible = true;
        head.visible=true;
        // name_bg.visible = true;
        // score_bg.visible = true;
        
        var sData = MjClient.data.sData;
        var tData = sData.tData;
        if (MjClient.isInGoldField() && (MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP || MjClient.getAppType() == MjClient.APP_TYPE.YLHUNANMJ ))
        {   
            if (name_bottom) {
                name_bottom.visible = true;
            }
            if (icon_bottom) {
                icon_bottom.visible = true;
            }
            
        }else if (head_bottom) {
            head_bottom.visible = true;
        }
        MjClient.loadWxHead(pl.info.uid, pl.info.headimgurl);
        setUserOffline(node, off);
        MjClient.playui.initUserHandUIPaoDeKuai(node, off);
    }
    else
    {
        head.visible=false;
        name.visible = false;
        coin.visible = false;
        offline.visible = false;
        coin.visible = false;
        var WxHead = nobody.getChildByName("WxHead");
        if(WxHead)
            WxHead.removeFromParent(true);
    }
};

PlayPanel_chuntian.prototype.initUserHandUIPaoDeKuai = function (node, off, needSort)
{
    if (cc.isUndefined(needSort))
        needSort = true;

    var sData = MjClient.data.sData;
    var tData = sData.tData;
    var pl = getUIPlayer(off);

    if(!pl)
    {
        return;
    }

    //初始化玩家金币和名称
    InitUserCoinAndName(node, off);
    setAreaTypeInfo(true);
    currentLeftCardCount_paodekuai(off);

    if(
        tData.tState != TableState.waitPut &&
        tData.tState != TableState.waitEat &&
        tData.tState != TableState.waitCard &&
        tData.tState != TableState.waitVieGuan
    )
    {
        return;
    }

    //添加手牌
    if(MjClient.rePlayVideo == -1)// 表示正常游戏
    {
        if (pl.mjhand && off == 0) {//只初始化自己的手牌
            var vcard = [];
            for (var i = 0; i < pl.mjhand.length; i++) {

                var card = getNewCard_card(node, "stand", "mjhand", pl.mjhand[i], off);
                var index = vcard.indexOf(pl.mjhand[i]);//区分2张一样的牌
                if(index >= 0)
                {
                    card.setUserData(1);
                }
                else
                {
                    card.setUserData(0);
                }
                vcard.push(pl.mjhand[i]);
            }

            if (tData.areaSelectMode.fangZuoBi && tData.lastPutPlayer == -1 && tData.curPlayer != getPlayerIndex(0))
            {
                MjClient.playui.isShowHandCardBeiMain = true;
                MjClient.playui.showHandCardBeiMian(true);
            }
        }
        else if (off > 0) {

        }
    }
    else
    {
        /*
            播放录像
        */
        if (pl.mjhand)
        {
            if(off == 0)
            {
                for (var i = 0; i < pl.mjhand.length; i++) {
                    getNewCard_card(node, "stand", "mjhand", pl.mjhand[i], off);
                }
            }
            else
            {
                for (var i = 0; i < pl.mjhand.length ; i++) {
                    getNewCard_card(node, "stand", "mjhand_replay", pl.mjhand[i], off);
                }
            }
        }
    }

    MjClient.playui.CardLayoutRestore(node, off, needSort);

    MjClient.playui.setJiaZhuNum(node,pl);
};


PlayPanel_chuntian.prototype.updateClockPosition = function(arrowNode)
{
    MjClient.playui._btnNoPut.setBright(false);
    MjClient.playui._btnNoPut.setTouchEnabled(false);
    var tData = MjClient.data.sData.tData;
    var uids = tData.uids;
    var curPlayerIndex = (tData.curPlayer + MjClient.MaxPlayerNum - tData.uids.indexOf(SelfUid())) % MjClient.MaxPlayerNum;

    // 如果是自己出牌，使用原来的位置
    if (curPlayerIndex == 0) {
        arrowNode.setPosition(arrowNode.srcPosition);
        return;
    }

    // 对方出牌时，将闹钟定位到对方头像旁边
    var curPlayerNode = getNode_cards(curPlayerIndex);
    if (curPlayerNode) {
        var headNode = curPlayerNode.getChildByName("head");
        if (headNode) {
            // 获取头像的世界坐标
            var headWorldPos = headNode.convertToWorldSpace(cc.p(0, 0));
            // 转换为闹钟父节点的本地坐标
            var clockLocalPos = arrowNode.getParent().convertToNodeSpace(headWorldPos);

            // 根据玩家位置调整闹钟偏移
            var headSize = headNode.getContentSize();
            var clockOffset = { x: 0, y: 0 };

            switch (curPlayerIndex) {
                case 1: // 右边玩家 - 闹钟放在头像左边
                    clockOffset.x = -arrowNode.getContentSize().width - 20;
                    clockOffset.y = headSize.height / 2;
                    break;
                case 2: // 上边玩家
                    clockOffset.x = headSize.width / 2;
                    clockOffset.y = headSize.height + 20;
                    break;
                case 3: // 左边玩家 - 闹钟放在头像右边
                    clockOffset.x = headSize.width + 20;
                    clockOffset.y = headSize.height / 2;
                    break;
                default:
                    clockOffset.x = headSize.width + 20;
                    clockOffset.y = headSize.height / 2;
                    break;
            }

            // 设置闹钟位置
            arrowNode.setPosition(clockLocalPos.x + clockOffset.x, clockLocalPos.y + clockOffset.y);

            cc.log("闹钟已移动到玩家", curPlayerIndex, "头像旁边，位置:", clockLocalPos.x + clockOffset.x, clockLocalPos.y + clockOffset.y);
        } else {
            // 如果找不到头像节点，使用原来的逻辑
            this.updateClockPositionFallback(arrowNode, curPlayerIndex);
        }
    } else {
        // 如果找不到玩家节点，使用原来的逻辑
        this.updateClockPositionFallback(arrowNode, curPlayerIndex);
    }

    // 清理出牌区域
    if (curPlayerNode != null && (tData.curPlayer !== tData.lastPutPlayer)) {
        var children = curPlayerNode.children;
        for (var i = 0; i < children.length; i++) {
            var ni = children[i];
            if(ni.name == "out")
                ni.removeFromParent(true);
        }
    }
}

// 闹钟位置更新的备用方案（原来的逻辑）
PlayPanel_chuntian.prototype.updateClockPositionFallback = function(arrowNode, curPlayerIndex) {
    var curPlayerNode = null;
    var deskCardPosOffset = {
        x: 44,
        y:-34
    }
    if (curPlayerIndex == 1)
        curPlayerNode = this._topNode;
    else if (curPlayerIndex == 2) {
        curPlayerNode = this._topNode;
        deskCardPosOffset.x = 0 - deskCardPosOffset.x;
    }

    if (curPlayerNode != null){
        var deskCardPos = curPlayerNode.getChildByName("deskCard").getPosition();
        if (!MjClient.isInGoldField())
        {
            if (isIPhoneX() && MjClient.rePlayVideo != -1) {
                deskCardPos.x += 41 * Math.min(MjClient.size.width/1280,MjClient.size.height/720) * (curPlayerIndex == 1 ? 2 : -1);
            } else {
                deskCardPos.y += deskCardPosOffset.y;
                deskCardPos.x += deskCardPosOffset.x;
            }
        }
        else{
            deskCardPos.x += 80 * (cc.director.getWinSize().width/1280) * (curPlayerIndex == 2? -1:1);
            deskCardPos.y += 30* (cc.director.getWinSize().height/720) ;
        }

        arrowNode.setPosition(deskCardPos);
    }
    else if (isIPhoneX() && MjClient.rePlayVideo != -1 && !MjClient.isInGoldField()) {
        var deskCardPos = this._topNode.getChildByName("deskCard").getPosition();
        deskCardPos.x -= 41 * Math.min(MjClient.size.width/1280,MjClient.size.height/720);
        deskCardPos.y = arrowNode.srcPosition.y;
        arrowNode.setPosition(deskCardPos);
    }
    else
        arrowNode.setPosition(arrowNode.srcPosition);
};

/**
 * 拼接游戏玩法及付费信息
 * @function
 * @return {String}
 */
PlayPanel_chuntian.prototype.getGameInfoString = function(param)
{
    return "";
    var tData = MjClient.data.sData.tData;
    var str = "";

    if (param != "roundInfo")
        str += MjClient.MaxPlayerNum == 3 ? "三人玩," : "两人玩,";
    str += tData.areaSelectMode.cardNumIndex == 0 ? "16张," : "15张,";
    str += tData.areaSelectMode.mustPut ? "" : "非必管,";
    str += tData.areaSelectMode.zhaDanBuChai ? "炸弹不可拆," : "";
    var firstOutRuleStr = "";
    switch (tData.areaSelectMode.firstPutRule){
        case 1:{
            firstOutRuleStr = tData.areaSelectMode.isPreRoundFirstRule==true?  "每局先出黑桃3,":"首局先出黑桃3,"
            break;
        }
        case 2:{
            firstOutRuleStr = ""
            break;
        }
        case 3:{
            firstOutRuleStr = tData.areaSelectMode.isPreRoundFirstRule==true?  "每局先出黑桃3,":"首局先出黑桃3,"
            break;
        }
        case 4:{
            firstOutRuleStr = tData.areaSelectMode.isPreRoundFirstRule==true?  "每局随机先手,":"首局随机先手,"
            break;
        }
        default:{
            firstOutRuleStr = ""
            break
        }
    };

    switch (tData.areaSelectMode.piaofen){
        case 1:{ str += "飘123,"; break; }
        case 2:{ str += "飘235,"; break; }
        case 3:{ str += "飘258,"; break; }
        case 4:{ str += "每局飘1,"; break; }
        case 5:{ str += "每局飘2,"; break; }
        default:{ str += ""; break }
    };

    // str += tData.areaSelectMode.firstHeiTao3 ? "首局先出黑桃三," : "";
    str += firstOutRuleStr;
    str += tData.areaSelectMode.can4dai2 ? "四带二," : "";
    str += tData.areaSelectMode.can4dai3 ? "四带三," : "";
    str += tData.areaSelectMode.hongTao10Niao ? "红桃10扎鸟," : "";
    str += tData.areaSelectMode.hongTao10JiaFen ? "红桃10加5分," :"";
    str += tData.areaSelectMode.can3aZhaDan ? "3个A算炸弹," : "";
    str += tData.areaSelectMode.isPlayerShuffle == 1 ? "手动切牌," : "系统切牌,";

    if (typeof(tData.areaSelectMode.fengDing) == "number") {
        switch (tData.areaSelectMode.fengDing)
        {
            case 1:
                str += "30/32分封顶,";
                break;
            case 2:
                str += "60/64分封顶,";
            case 3:
                str += "120/128分封顶,"; 
                break;
        }
    }

    str += tData.areaSelectMode.paodekuaiTY_difen ? "底分X" + tData.areaSelectMode.paodekuaiTY_difen + ","  : "";
    str += tData.areaSelectMode.fangQiangGuan ? "防强关," : "";
    str += tData.areaSelectMode.baoDanPutMax ? "报单必大," : "";

    if (param != "roundInfo")
    {
        str += tData.areaSelectMode.fangZuoBi ? "防作弊," : "";
        str += tData.areaSelectMode.showCardNumber ? "显示牌数," : "";
    }

    if(tData.areaSelectMode.fanBei == 1)
    {
        str += "小于" + tData.areaSelectMode.fanBeiScore + "分翻倍,";
    } 

    if (param != "roundInfo")
    {
        switch (tData.areaSelectMode.payWay)
        {
            case 0:
                str += "房主付";
                break;
            case 1:
                str += "AA付";
                break;
            case 2:
                str += "大赢家付";
                break;
        }
    }

    if(tData.areaSelectMode.trustTime > 0)
    {
        str += Math.floor(tData.areaSelectMode.trustTime/60) + "分钟,";
    } 

    if(tData.areaSelectMode.isTrustWhole && tData.areaSelectMode.trustWay >= 0)
    {
        str += ["托管当局,", "托管当局+下一局,", "整场托管,"][tData.areaSelectMode.trustWay] || "";
    } 


    if (str.charAt(str.length - 1) == ",")
        str = str.substring(0, str.length - 1);

    //比赛场
    var BSStr = "";
    if(tData.matchId){
        BSStr = ",10秒出牌";
        str += BSStr;
        str = GameCnName[MjClient.gameType]+","+str;
    }

    if(MjClient.isInGoldField())
    {
        str = "";
        str += tData.areaSelectMode.cardNumIndex == 0 ? "16张" : "15张";
        str += tData.areaSelectMode.paodekuaiTY_difen ? ",底分X" + tData.fieldBase: "";
    }
    return str;
};

PlayPanel_chuntian.prototype.shwoFlyCardAnim = function(flyNode)
{
    var tData = MjClient.data.sData.tData;
    var off = getOffByIndex(tData.curPlayer);
    var playerNode = getNode_cards(off);
    if (!playerNode)
        return;

    if(4 == tData.areaSelectMode.firstPutRule)     // 2人完 随机先手，可以不出3，不播放黑桃3加入手牌的动画
        return;

    var headNode = playerNode.getChildByName("head");
    var point = headNode.convertToWorldSpace(cc.p(headNode.width/2, headNode.height/2));
    point = flyNode.getParent().convertToNodeSpace(point);
    setCardSprite_card(flyNode, 12, false);
    flyNode.setVisible(true);
    flyNode.setScale(flyNode.getScale() * 1.5);
    flyNode.runAction(cc.sequence(cc.delayTime(0.2), cc.spawn(cc.moveTo(1.0, point), cc.scaleTo(1.0, flyNode.getScale()/1.5)), cc.callFunc(function() {
        flyNode.setVisible(false);

        if(!MjClient.playui.isFaPai)
        {
            postCardsEnded();
        }
    })));
}

// 播放先手标识动画
PlayPanel_chuntian.prototype.showOffenseAnim = function()
{
    var tData = MjClient.data.sData.tData;
    var off = getOffByIndex(tData.curPlayer);
    var playerNode = getNode_cards(off);
    if (!playerNode)
        return;

    var first = new cc.Sprite("playing/paodekuaiTable_new/i_am_first.png");
    first.setAnchorPoint(cc.p(0.5,0.5));
    MjClient.playui.addChild(first,10);

    var headNode = playerNode.getChildByName("head");
    var offLine = headNode.getChildByName("offline");
    var point = offLine.convertToWorldSpace(cc.p(offLine.width/2, offLine.height/2));
    var winSize = cc.director.getWinSize();

    if (point.x < winSize.width/2) {
        // 上边、下边的玩家
        setWgtLayout(first, [first.width / 1280, first.height / 720], [0, point.y/winSize.height], [-0.5, 0]);

        first.runAction(cc.sequence(cc.moveBy(1, point.x + headNode.getBoundingBox().width/2 + first.getBoundingBox().width, 0).easing(cc.easeBackOut()), cc.callFunc(function() {
            first.removeFromParent();
        })));
    } else {
        // 右边的玩家
        setWgtLayout(first, [first.width / 1280, first.height / 720], [1, point.y/winSize.height], [0.5, 0]);

        first.runAction(cc.sequence(cc.moveBy(1, -(winSize.width - point.x + headNode.getBoundingBox().width/2 + first.getBoundingBox().width), 0).easing(cc.easeBackOut()), cc.callFunc(function() {
            first.removeFromParent();
        })));
    }
}

//金币场每局获取分数的动画
PlayPanel_chuntian.prototype.showGetScoreAni_filedQuick = function (off)
{   
    var headNode = getNode_cards(off).getChildByName('head');
    if(headNode.getChildByName('scoreText')){
        headNode.removeChildByName('scoreText');
    }
    var scoreText = new ccui.Text();
    var pl = getUIPlayer(off);
    if(pl.winone < 0){
        scoreText.setString(pl.winone);
        scoreText.setColor(cc.color("#ffffff"));
    }else{
        scoreText.setString('+' + pl.winone);
        scoreText.setColor(cc.color("#fff000"));
    }
    scoreText.setFontName("fonts/lanting.ttf");
    scoreText.setName("scoreText");
    scoreText.setFontSize(40);
    scoreText.setOpacity(0.5);
    if(off == 0 || off == 2){
        scoreText.setPosition(200,  0);
    }else if(off == 1){
        scoreText.setPosition(-70,  0);
    }
    headNode.addChild(scoreText);

    scoreText.runAction(cc.sequence(cc.fadeTo(0.3, 255), cc.delayTime(0.8), cc.fadeTo(0.2, 0), cc.callFunc(function () {
        scoreText.removeFromParent();
    }.bind(this))));

}
//处理金币场快速赛牌局结束时布局
PlayPanel_chuntian.prototype.cardLayoutRestore_endfiledQuick = function (off)
{
    var node = getNode_cards(off);
    //隐藏牌数标记
    var tingCard = node.getChildByName('head').getChildByName("tingCard");
    if(tingCard){
        tingCard.visible = false;
    }

    //隐藏报警器
    this._spriteSingle.visible = false;

    //清除牌桌打出的牌和手牌
    var children = node.children;
    for (var i = 0; i < children.length; i++)
    {
        var ni = children[i];
        if(ni.name == "out" || ni.name == "mjhand")
        {
            ni.removeFromParent(true);
        }
    }
}
//处理金币场牌局结束时布局
PlayPanel_chuntian.prototype.cardLayoutRestore_endfiled = function (off)
{
    var node = getNode_cards(off);
    //隐藏牌数标记
    var tingCard = node.getChildByName('head').getChildByName("tingCard");
    if(tingCard){
        tingCard.visible = false;
    }

    //清除牌桌打出的牌
    var children = node.children;
    for (var i = 0; i < children.length; i++)
    {
        var ni = children[i];
        if(ni.name == "out")
        {
            ni.removeFromParent(true);
        }
    }

    if(off == 0){//自己不用展示手牌
        return;
    }

    var pl = getUIPlayer(off);
    if(!pl || !pl.mjhand || pl.mjhand.length <= 0)
    {
        return;
    }

    //展示手牌
    var _deskCard = node.getChildByName("deskCard");
    var _showCards = [];
    for(var i = 0; i < pl.mjhand.length;i++)
    {
        var out = _deskCard.clone();
        out.setScale(out.getScale()*1.3);
        out.visible = true;
        if( !MjClient.isInGoldFieldNormal() && !MjClient.isInGoldFieldQuick() 
            && (MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP || 
            MjClient.getAppType() == MjClient.APP_TYPE.QXSYDTZ) ){
            out.name = "out";
        }
        setCardSprite_card(out, pl.mjhand[i], 0,true);
        node.addChild(out);
        _showCards.push(out);
    }

    var sort = function (node)
    {
        var pointCounts = {};
        for (var i = 0; i < node.length; i++) {
            var p = MjClient.majiang.calPoint(node[i].tag);
            if (pointCounts[p])
                pointCounts[p] ++;
            else
                pointCounts[p] = 1;
        }

        var commonCmp = function (a, b) {
            var c1 = pointCounts[MjClient.majiang.calPoint(a.tag)];
            var c2 = pointCounts[MjClient.majiang.calPoint(b.tag)];
            if (c1 == c2)
                return MjClient.majiang.cardValueCmp(a.tag, b.tag);
            else
                return c1 - c2;
        }

        node.sort(function(a, b) { return -commonCmp(a, b);});
    }

    sort(_showCards);

    var outSize = _showCards[0].getSize();
    var outScale = _showCards[0].scale;
    var width = outSize.width * outScale * 0.4;
    var height = outSize.height * outScale * 0.55;
    var initPosX = 0;
    var areaWidth = (_showCards.length - 1) * width + outSize.width * outScale;
    if(_showCards.length > 10){
        areaWidth = 9 * width + outSize.width * outScale;
    }
    switch (off)
    {
        case 1:
            initPosX = _deskCard.x - areaWidth + outSize.width * outScale;
            break;
        case 2:
            initPosX = _deskCard.x;
            break;
    }

    var startX = initPosX;
    var startY = _deskCard.y;
    for(var i = 0; i < _showCards.length; i++)
    {
        _showCards[i].x = startX;
        _showCards[i].y = startY;
        _showCards[i].zIndex = i*2;
        startX += width;
        if(i == 9){
            startX = initPosX;
            startY = _deskCard.y - height;
        }
    }

    if(pl.mjdesc3 == "大关" || pl.mjdesc3 == "小关"){
        var _spPosX = initPosX + areaWidth / 2 - 50;
        var _spPosY  = startY - 15;

        if(MjClient.gameType == MjClient.GAME_TYPE.PAO_DE_KUAI_ELEVEN){
            _spPosY += 30;
        }

        var _index = (_showCards.length >= 10) ? (_showCards.length - 2)/2 : (_showCards.length/2 - 1);
        var _path = "gameOver/xiaoguan.png";
        if(pl.mjdesc3 == "大关"){
            _path = "gameOver/daguan.png";
        }

        if (!jsb.fileUtils.isFileExist(_path)){
            return;
        }

        var _image = new ccui.ImageView(_path);
        _image.setName("guan_Info");
        _image.setScale(0.8 * Math.min(MjClient.size.width/1280,MjClient.size.height/720));

        _image.zIndex = 9999;
        _image.setPosition(_spPosX, _spPosY);
        node.addChild(_image)
    }
}


PlayPanel_chuntian.prototype.CardLayoutDesk = function(node,cards,off)
{
    //if(off != 0) return;
    var children = node.children;
    var initDesk_y = node.getChildByName("deskCard").y;
    for(var i = 0; i < children.length; i++)
    {
        var ci = children[i];
        if(ci.name == "out")
            ci.y = initDesk_y;
    }

    var outStand = node.getChildByName("deskCard");
    outStand.visible = false;

    var uiOut = [];
    var uiHun = [];//癞子牌在最左边

    for(var i = 0; i < children.length; i++)
    {
        var ci = children[i];
        if(ci.name != "out")
            continue;

        if(MjClient.data.sData.tData.hunCard == ci.tag)
            uiHun.push(ci);
        else
            uiOut.push(ci);
    }

    if (uiHun.length + uiOut.length == 0)
        return;

    var sort = function (node)
    {
        var pointCounts = {};
        for (var i = 0; i < node.length; i++) {
            var p = MjClient.majiang.calPoint(node[i].tag);
            if (pointCounts[p])
                pointCounts[p] ++;
            else
                pointCounts[p] = 1;
        }

        var commonCmp = function (a, b) {
            var c1 = pointCounts[MjClient.majiang.calPoint(a.tag)];
            var c2 = pointCounts[MjClient.majiang.calPoint(b.tag)];
            if (c1 == c2)
                return MjClient.majiang.cardValueCmp(a.tag, b.tag);
            else
                return c1 - c2;
        }

        node.sort(function(a, b) { return -commonCmp(a, b);});
    }

    sort(uiOut);

    if(uiHun.length > 0) //是否有柰子，有则放在最前面 by sking
    {
        for(var i = 0; i < uiHun.length; i++)
        {
            uiOut.unshift(uiHun[i]); //向数组开头添加一个元素 unshift
        }
    }

    var cards = [];
    for (var i = 0; i < uiOut.length; i ++)
    {
        cards[i] = uiOut[i].tag;
    }

    var outSize = uiOut[0].getSize();
    var outScale = uiOut[0].scale;

    var cardType = MjClient.majiang.cardsType(cards, MjClient.data.sData.tData.areaSelectMode);
    var width = outSize.width * outScale * 0.4;
   
    var x = 0;

    var areaWidth = (uiOut.length - 1) * width + outSize.width * outScale;
    if (cardType == MjClient.majiang.CARDTPYE.sandaiyi || cardType == MjClient.majiang.CARDTPYE.sidaier)
        areaWidth += outSize.width * outScale * 1.05;

    switch (off)
    {
        case 0:
            x = outStand.x - areaWidth/2 + outSize.width * outScale / 2;
            break;
        case 1:
            x = outStand.x - areaWidth + outSize.width * outScale;
            if(MjClient.rePlayVideo == -1)  // 表示正常游戏
            {
                if (uiOut.length >= 7)
                    x += width;
            }
            break;
        case 2:
            x = outStand.x;
            x = outStand.x + outSize.width/2 * outScale;
            if(MjClient.rePlayVideo == -1)  // 表示正常游戏
            {
                if (uiOut.length >= 6)
                    x -= width;
            }
            break;
        case 3:
            x = outStand.x;
            break;
    }

    //设置麻将大小
    for(var i = 0; i < uiOut.length; i++)
    {
        uiOut[i].x = x;
        uiOut[i].zIndex = i*2;

        if ((cardType == MjClient.majiang.CARDTPYE.sandaiyi && i == 2) || (cardType == MjClient.majiang.CARDTPYE.sidaier && i == 3))
            x += outSize.width * outScale * 1.05;
        else
            x += width;
    }
    MjClient.initDesk_y = "undefined";
};

//头像位置
PlayPanel_chuntian.prototype.reConectHeadLayout_paodekuaiTY = function (node)
{
    var sData = MjClient.data.sData;
    if(MjClient.isInGoldField())
        this.reConectHeadLayout_PaoDeKuai_fieldId(node);
    else
        reConectHeadLayout_card(node);
}

//时间节点设置
PlayPanel_chuntian.prototype.BgTime_run = function (node)
{
    var text = new ccui.Text();
    if(MjClient.getAppType() == MjClient.APP_TYPE.QXYYQP || MjClient.getAppType() == MjClient.APP_TYPE.YLHUNANMJ) {//岳阳同一使用方正兰亭
        text.setFontName("fonts/lanting.TTF");
    }else{
        text.setFontName(MjClient.fzcyfont);
    }
    text.setFontSize(18);
    text.setTextColor(cc.color(222,226,199));
    text.setAnchorPoint(0.5,0.5);
    text.setPosition(node.getContentSize().width / 2, node.getContentSize().height / 2);
    node.addChild(text);
    text.schedule(function(){
        
        var time = MjClient.getCurrentTime();
        var str = (time[3]<10?"0"+time[3]:time[3])+":"+
            (time[4]<10?"0"+time[4]:time[4]);
        this.setString(str);
    });
}

//设置红包活动按钮位置
PlayPanel_chuntian.prototype.setHongBaoPos = function (hongbaoBtn)
{
    if (!MjClient.playui.RightTopBG || !hongbaoBtn) return;
    hongbaoBtn.x = (1280 -  MjClient.playui.RightTopBG.width-50)
}

//////////////////////////金币场UI调整//////////////////////////////////////////////////////////////////////////////////////
PlayPanel_chuntian.prototype.reConectHeadLayout_PaoDeKuai_fieldId= function (node)
{
    var down = node.getChildByName("down").getChildByName("head");
    var top = node.getChildByName("top").getChildByName("head");
    var right = node.getChildByName("right").getChildByName("head");

    setWgtLayout(right, [0.13, 0.13], [1, 1.0], [-4.1, -0.6], false, false);
    if (isIPhoneX()) {
        setWgtLayout(down, [0.13, 0.13], [0.00, 0.0], [0.6, 0.65], false, false);       
        setWgtLayout(top, [0.13, 0.13], [0.00, 1.0], [0.6, -2.0], false, false);
    }else{
        setWgtLayout(down, [0.13, 0.13], [0, 0.0], [0.6, 0.65], false, false);
        setWgtLayout(top, [0.13, 0.13], [0, 1.0], [0.6, -2.0], false, false);
    }

    var sData = MjClient.data.sData;
    var tData = sData.tData;
    if (!MjClient.isInGoldField() || (MjClient.getAppType() != MjClient.APP_TYPE.QXYYQP && MjClient.getAppType() != MjClient.APP_TYPE.YLHUNANMJ) )
    {
        var head_bottom = down.getChildByName("head_bottom");
        var coinbg = down.getChildByName("coinbg");
        if (!head_bottom || !coinbg)return;
        head_bottom.visible = false;
        coinbg.visible = true;
    }
}

// PlayPanel_chuntian.prototype.initFieldIdUI = function (node)
// {
//     var sData = MjClient.data.sData;
//     var tData = sData.tData;
//     var fieldNode = node.getChildByName("FieldNode");
//     if (!fieldNode) return;
//     if (MjClient.isInGoldField())
//     {
//         fieldNode.visible=true;
//     }
//     else{
//         fieldNode.visible=false;
//         return;
//     }
//     MjClient.playui.RightTopBG = fieldNode.getChildByName("RightTopBG");
//     this.BindFieldIdUI(fieldNode);

//     //隐藏金币场不需要的节点
//     node.getChildByName("banner").visible = false;
//     node.getChildByName("voice_btn").visible = false;
//     node.getChildByName("chat_btn").visible = false;

//     //金币场节点位置调整
//     var bottonBG = fieldNode.getChildByName("bottonBG");
//     var offH = bottonBG.height*bottonBG.scaleY;

//     node.getChildByName("noPutTips").y += offH;
//     node.getChildByName("BtnReady").y += offH;
//     node.getChildByName("BtnNoPut").y += offH;
//     node.getChildByName("BtnPutCard").y += offH;
//     node.getChildByName("BtnHimt").y += offH;

//     var down = node.getChildByName("down");
//     down.getChildByName("play_tips").y += offH;
//     down.getChildByName("ready").y += offH;
//     down.getChildByName("stand").y += (offH + 10 * bottonBG.scaleY);
//     down.getChildByName("deskCard").y += offH;
//     down.getChildByName("noPutTag").y += offH;

//     var block_tuoguan = node.getChildByName("block_tuoguan");
//     block_tuoguan.getChildByName("btn_tuoguan").y += offH;
//     block_tuoguan.getChildByName("Text_1").y += offH;

//     MjClient.clockNode.srcPosition.y += offH;
// }

// PlayPanel_chuntian.prototype.BindFieldIdUI = function (node)
// {
//     var uibind = {
//         bottonBG:
//         {
//             _run: function() {
//                 setWgtLayout(this, [this.width/1280, 0], [0.0, 0.0], [0, 0]);
//             },
//             chat_btn: {
//                 _click: function() {
//                     var chatlayer = new ChatLayer();
//                     MjClient.Scene.addChild(chatlayer);
//                 }
//             },
//             roundnumAtlas:{
//                 _visible : function () {
//                     var sData = MjClient.data.sData;
//                     var tData = sData.tData;
//                     if (tData){
//                         if(MjClient.isInGoldFieldQuick()) {//金币场显示场次名称
//                             return true;
//                         }
//                     }
//                     return false;
//                 },
//                 _run: function() {
//                     this.ignoreContentAdaptWithSize(true);
//                 },
//                 _text: function() {
//                     var sData = MjClient.data.sData;
//                     var tData = sData.tData;
//                     if (tData) return "第" + (tData.roundAll-tData.roundNum + 1)+"/"+tData.roundAll + "局";
//                 },
//                 _event: {
//                     mjhand: function() {
//                         var sData = MjClient.data.sData;
//                         var tData = sData.tData;
//                         if (tData) return this.setString("第" + (tData.roundAll-tData.roundNum + 1)+"/"+tData.roundAll + "局");
//                     }
//                 }
//             },
//             jipai_btn: {
//                 _run: function() {
//                     this.visible = false;
//                 },
//                 _click: function() {
//                     var jiPaiQiPanel = MjClient.playui.getChildByName("jiPaiQiPanel");

//                     // postEvent("jiPaiQiOperation",!jiPaiQiPanel.isJiPaiQiShowing());

//                     if (jiPaiQiPanel.isJiPaiQiShowing()) {
//                         postEvent("jiPaiQiOperation",false);
//                     } else if (MjClient.playui.isUsingJiPaiQi()) {
//                         postEvent("jiPaiQiOperation",true);
//                     } else {
//                         var useJipaiqi = function () {
//                             cc.log("this is in useJipaiqi");

//                             // 使用记牌器
//                             MjClient.gamenet.request("pkroom.handler.tableMsg", {cmd: "useJipaiqi"}, 
//                                 function(rtn) {
//                                     if (rtn.code == 0) {
//                                         // postEvent("jiPaiQiOperation",true);
//                                     } else if (rtn.message) {
//                                         MjClient.showToast(rtn.message);
//                                     }
//                                 }
//                             );
//                         }
//                         if (MjClient.data.pinfo.jipaiqi1 > 0 || MjClient.data.pinfo.jipaiqi2 > 0) {
//                             // 直接使用
//                             useJipaiqi();
//                         } else {
//                             // 先购买
//                             MjClient.gamenet.request("pkplayer.handler.getRechargeLadder", {type: "jipaiqi"}, 
//                                 function(rtn) {
//                                     if (rtn.code == 0) {
//                                         var validCommoditys = MjClient.playui.checkJiPaiQiListValid(rtn.data);

//                                         if (validCommoditys)
//                                             MjClient.playui.showJiPaiQiBuyingPanel(validCommoditys,useJipaiqi)
//                                         else
//                                             MjClient.showToast("无可用记牌器");
//                                     } else if (rtn.message) {
//                                         MjClient.showToast(rtn.message);
//                                     }
//                                 }
//                             );
//                         }
//                     }
//                 },
//                 _event:{
//                     mjhand: function() {
//                         MjClient.playui.setJiPaiQiEnabled(this);
//                         MjClient.playui.checkJiPaiQiButtonVisible(this);
//                     },
//                     waitPut: function() {
//                         MjClient.playui.setJiPaiQiEnabled(this);
//                     },
//                     initSceneData: function()
//                     {
//                         MjClient.playui.setJiPaiQiEnabled(this);
//                         MjClient.playui.checkJiPaiQiButtonVisible(this);
//                     },
//                     PostCardsEnded: function()
//                     {
//                         MjClient.playui.setJiPaiQiEnabled(this);
//                     }
//                 },
//                 jiPaiQiNum: {
//                     _run: function() {
//                         MjClient.playui.setJiPaiQiNum(this);
//                     },
//                     _event: {
//                         updateInfo: function() {
//                             MjClient.playui.setJiPaiQiNum(this);
//                         }
//                     }
//                 }
//             },
//         },
//         LeftTopBG:
//         {
//             _run: function() {
//                 setWgtLayout(this, [this.width/1280, 0], [0.0, 1.0], [0, 0]);
//             },
//             bg_time:{
//                 _run:function() {
//                     MjClient.playui.BgTime_run(this);
//                }
//             },

//             wifi: {
//                 _run: function() {
//                     MjClient.playui.updateWifiState_field(this);
//                 }
//             }
//         },
//         RightTopBG:
//         {
//             _run: function() {
//                 setWgtLayout(this, [this.width/1280, 0], [1.0, 1.0], [0, 0]);
//             },
//             setting: {
//                 _click: function() {
//                     var settringLayer = new SettingView();
//                     settringLayer.setName("PlayLayerClick");
//                     MjClient.Scene.addChild(settringLayer);
                    
//                 },
//             },
//             get_gold_btn:{
//                 _run: function() {
//                     this.setVisible(false);
//                 },
//                 _click: function() {
//                 }
//             },
//             tuoguan_btn:{
//                 _run:function () {
//                     this.setVisible(MjClient.isInGoldField());
//                 },
//                 _click: function() {
//                     MjClient.gamenet.request("pkroom.handler.tableMsg", {cmd: "beTrust"});
//                 },
//                 _event:{
//                     beTrust:function (msg) {
//                         if(msg.uid == SelfUid()){
//                             this.enabled = false;
//                         }
//                     },
//                     cancelTrust:function (msg) {
//                         if(msg.uid == SelfUid()){
//                             this.enabled = true;
//                         }
//                     },
//                     initSceneData:function (msg) {
//                         var pl = getUIPlayer(0);
//                         if(pl.trust){
//                             this.enabled = false;
//                         }else {
//                             this.enabled = true;
//                         }
//                     },
//                 },
//             },
//             duty_btn: {
//                 _run: function () {
//                     if (MjClient.isInGoldField())
//                     {
//                         this.setVisible(true);
//                     }
//                     else {
//                         this.setVisible(false);
//                         return;
//                     }

//                     var that = this
//                     ShowDayTaskTips(this)
//                 },
//                 _click: function () {
//                     MjClient.Scene.addChild(new GoldTaskLayer());
//                 },
//                 Image_hongdian:{
//                     _run: function () {
//                         this.visible = MjClient._GoldFuli;
//                     },
//                     _event: {
//                         refresh_mission: function() {
//                             this.visible = MjClient._GoldFuli;
//                         }
//                     }
//                 }
//             },
//         }
//     }
//     BindUiAndLogic(node, uibind);
// }

// 更新wifi信号 3张图片
PlayPanel_chuntian.prototype.updateWifiState_field = function(node)
{
    var callback = function()
    {
        var ms = MjClient.reqPingPong / 1000.0;
        if(ms < 0.3)
            node.loadTexture("playing/pdkfield/wf.png");
        else if(ms < 0.6)
            node.loadTexture("playing/pdkfield/wf3.png");
        else
            node.loadTexture("playing/pdkfield/wf4.png");
    };
    node.runAction(cc.repeatForever(cc.sequence(cc.callFunc(callback), cc.delayTime(5))));
}


PlayPanel_chuntian.prototype.reLoadOptBtnRes = function() {
    //出牌按钮
    if(this._btnPutCard) this._btnPutCard.loadTextures("playing/chuntiangametable/chupai.png", null, "playing/chuntiangametable/chupai_2.png");
    //提示按钮  
    if(this._btnHimt) this._btnHimt.loadTextures("playing/chuntiangametable/tishi.png", null, "playing/chuntiangametable/tishi_s.png");
     //不出按钮   
    if(this._btnNoPut) this._btnNoPut.loadTextures("playing/chuntiangametable/buchu.png", null, "playing/chuntiangametable/buchu_s.png");
   
}

PlayPanel_chuntian.prototype.reLoadOptBtnRes2 = function() {
    cc.log("进入reLoadOptBtnRes2")
    var tData = MjClient.data.sData.tData;
    var isbaochunNode = MjClient.playui.jsBind.isbaochun;
    var btn_baoChun = isbaochunNode.btn_baoChun._node;
    var btn_buBaoChun = isbaochunNode.btn_buBaoChun._node;

    // 如果正在等待庄家确认，不要重新加载按钮资源
    if (tData.baoChunStatus === "waitingZhuangConfirm") {
        cc.log("正在等待庄家确认，不重新加载按钮资源");
        return;
    }

    cc.log("111222",JSON.stringify(tData)+"isbaochunNode"+JSON.stringify(isbaochunNode))

    // 正确判断庄家：使用庄家索引而不是当前出牌玩家
    var selfUid = SelfUid();
    var zhuangUid = tData.uids[tData.zhuang];
    var isZhuang = (selfUid === zhuangUid);

    if (isZhuang) {
        cc.log("看看进没",btn_baoChun.loadTextures)
        // 只有庄家更换图片
         if(btn_buBaoChun) btn_buBaoChun.loadTextures("playing/chuntiangametable/tongyi.png",null,"playing/chuntiangametable/butongyi.png");
        cc.log("111222",JSON.stringify(btn_buBaoChun))
        // btn_buBaoChun.loadTextures && btn_buBaoChun.loadTextures("playing/chuntiangametable/tongyi.png",null,"playing/chuntiangametable/butongyi.png");
        btn_baoChun.loadTextures && btn_baoChun.loadTextures("playing/chuntiangametable/butongyi.png",null,"playing/chuntiangametable/tongyi.png");
    }else{
        cc.log("看看进不")
        btn_baoChun.loadTextures && btn_baoChun.loadTextures("playing/chuntiangametable/btn_bg_Green.png",null,"playing/chuntiangametable/btn_bg_Green.png");
        btn_buBaoChun.loadTextures && btn_buBaoChun.loadTextures("playing/chuntiangametable/btn_bg_Blue.png",null,"playing/chuntiangametable/btn_bg_Blue.png");
    }
}
// 修改原有的报春流程，移除直接开始游戏的逻辑
PlayPanel_chuntian.prototype.startWaitBaoChun = function(msg) {
    cc.log("=== startWaitBaoChun函数开始执行 ===");
    cc.log("传入的msg:", JSON.stringify(msg));

    // 清除之前的报春标识
    this.hideAllBaoChunIcons();

    var tData = MjClient.data.sData.tData;
    cc.log("当前tData.baoChunStatus:", tData.baoChunStatus);
    cc.log("当前tData.zhuang:", tData.zhuang);
    cc.log("当前tData.uids:", tData.uids);

    // 如果当前状态不是等待报春，不显示按钮
    if (tData.baoChunStatus !== "waiting") {
        cc.log("❌ 当前状态不是等待报春，不显示按钮，状态:", tData.baoChunStatus);
        return;
    }

    // 检查当前玩家是否是庄家
    var selfUid = SelfUid();
    var zhuangUid = tData.uids[tData.zhuang];
    cc.log("自己UID:", selfUid, "庄家UID:", zhuangUid);
    if (selfUid === zhuangUid) {
        // cc.log("❌ 当前玩家是庄家，不能进行报春选择");
        // MjClient.showToast("庄家不能报春，等待其他玩家选择...");
        return;
    }

    cc.log("✅ 当前玩家不是庄家，可以进行报春选择");

    cc.log("🔍 尝试获取报春按钮节点...");
    cc.log("MjClient.playui:", MjClient.playui);
    cc.log("MjClient.playui.jsBind:", MjClient.playui.jsBind);

    var isbaochunNode = MjClient.playui.jsBind.isbaochun;
    cc.log("isbaochunNode:", isbaochunNode);

    if (!isbaochunNode) {
        cc.log("❌ 错误：找不到isbaochun节点！");
        cc.log("可用的jsBind节点:", Object.keys(MjClient.playui.jsBind));
        return;
    }

    cc.log("isbaochunNode的属性:", Object.keys(isbaochunNode));
    cc.log("btn_baoChun对象:", isbaochunNode.btn_baoChun);
    cc.log("btn_buBaoChun对象:", isbaochunNode.btn_buBaoChun);

    var btn_baoChun = isbaochunNode.btn_baoChun._node;
    var btn_buBaoChun = isbaochunNode.btn_buBaoChun._node;
    cc.log("btn_baoChun._node:", btn_baoChun);
    cc.log("btn_buBaoChun._node:", btn_buBaoChun);

    if (btn_baoChun && btn_buBaoChun) {
        cc.log("✅ 成功获取到两个按钮节点");

        // 清除之前的事件监听器
        cc.log("🧹 清除之前的事件监听器...");
        btn_baoChun.removeAllChildren();
        btn_buBaoChun.removeAllChildren();
        btn_baoChun.removeAllTouchEventListeners && btn_baoChun.removeAllTouchEventListeners();
        btn_buBaoChun.removeAllTouchEventListeners && btn_buBaoChun.removeAllTouchEventListeners();

        cc.log("👁️ 设置按钮可见...");
        btn_baoChun.visible = true;
        btn_buBaoChun.visible = true;
        cc.log("按钮可见状态 - btn_baoChun:", btn_baoChun.visible, "btn_buBaoChun:", btn_buBaoChun.visible);

        // 加载玩家报春选择的按钮图片
        btn_baoChun.loadTextures && btn_baoChun.loadTextures("playing/chuntiangametable/btn_bg_Green.png", null, "playing/chuntiangametable/btn_bg_Green.png");
        btn_buBaoChun.loadTextures && btn_buBaoChun.loadTextures("playing/chuntiangametable/btn_bg_Blue.png", null, "playing/chuntiangametable/btn_bg_Blue.png");

        // 禁用出牌按钮
        var btnPutCard = MjClient.playui.jsBind.BtnPutCard._node;
        if(btnPutCard) {
            if (btnPutCard.setEnabled) {
                btnPutCard.setEnabled(false);
            } else {
                btnPutCard.enabled = false;
            }
        }

        // 添加倒计时显示
        this.startBaoChunCountdown(msg.timeLimit || 15);

        // 绑定点击事件
        btn_baoChun.addTouchEventListener(function(sender, type) {
            if (type == 2) {
                this.clearBaoChunCountdown();
                btn_baoChun.visible = false;
                btn_buBaoChun.visible = false;

                // 播放报春音效
                playBaoChunEffect_chuntian(1, SelfUid()); // 1=报春

                MjClient.gamenet.request("pkroom.handler.tableMsg", {
                    cmd: "baoChun",
                    isBaoChun: true
                });
            }
        }.bind(this));

        btn_buBaoChun.addTouchEventListener(function(sender, type) {
            if (type == 2) {
                this.clearBaoChunCountdown();
                btn_baoChun.visible = false;
                btn_buBaoChun.visible = false;

                // 播放不报春音效
                playBaoChunEffect_chuntian(0, SelfUid()); // 0=不报

                MjClient.gamenet.request("pkroom.handler.tableMsg", {
                    cmd: "baoChun",
                    isBaoChun: false
                });
            }
        }.bind(this));
    } else {
        cc.log("❌ 无法获取到按钮节点！");
        cc.log("btn_baoChun:", btn_baoChun);
        cc.log("btn_buBaoChun:", btn_buBaoChun);
        if (!btn_baoChun) cc.log("btn_baoChun为空");
        if (!btn_buBaoChun) cc.log("btn_buBaoChun为空");
    }
};

// 庄家确认报春界面
PlayPanel_chuntian.prototype.startWaitZhuangConfirm = function(msg) {
    var selfUid = SelfUid();
    var isbaochunNode = MjClient.playui.jsBind.isbaochun;
    var btn_baoChun = isbaochunNode.btn_baoChun._node;
    var btn_buBaoChun = isbaochunNode.btn_buBaoChun._node;

    // 强制隐藏所有按钮，清除之前的事件监听器
    if (btn_baoChun) {
        btn_baoChun.visible = false;
        btn_baoChun.removeAllChildren();
        // 移除所有事件监听器
        btn_baoChun.removeAllTouchEventListeners && btn_baoChun.removeAllTouchEventListeners();
    }
    if (btn_buBaoChun) {
        btn_buBaoChun.visible = false;
        btn_buBaoChun.removeAllChildren();
        // 移除所有事件监听器
        btn_buBaoChun.removeAllTouchEventListeners && btn_buBaoChun.removeAllTouchEventListeners();
    }

    // 只有庄家才显示确认界面
    if (selfUid !== msg.zhuangUid) {
        // MjClient.showToast("等待庄家确认报春...");
        return;
    }

    // 庄家显示确认按钮
    if (btn_baoChun && btn_buBaoChun) {
        btn_baoChun.visible = true;
        btn_buBaoChun.visible = true;

        // 加载庄家确认的按钮图片
        btn_baoChun.loadTextures && btn_baoChun.loadTextures("playing/chuntiangametable/tongyi.png", null, "playing/chuntiangametable/tongyi.png");
        btn_buBaoChun.loadTextures && btn_buBaoChun.loadTextures("playing/chuntiangametable/butongyi.png", null, "playing/chuntiangametable/butongyi.png");

        // 禁用出牌按钮
        var btnPutCard = MjClient.playui.jsBind.BtnPutCard._node;
        if(btnPutCard) {
            if (btnPutCard.setEnabled) {
                btnPutCard.setEnabled(false);
            } else {
                btnPutCard.enabled = false;
            }
        }

        // 添加庄家确认倒计时显示
        this.startZhuangConfirmCountdown(msg.timeLimit || 15);

        // 绑定点击事件 - btn_baoChun现在是"同意"按钮
        btn_baoChun.addTouchEventListener(function(sender, type) {
            if (type == 2) {
                this.clearBaoChunCountdown();
                btn_baoChun.visible = false;
                btn_buBaoChun.visible = false;
                cc.log("庄家点击同意报春");

                // 播放同意音效
                playBaoChunEffect_chuntian(3, SelfUid()); // 3=同意

                MjClient.gamenet.request("pkroom.handler.tableMsg", {
                    cmd: "baoChun",
                    agree: true,
                    isZhuangConfirm: true
                });
            }
        }.bind(this));

        // btn_buBaoChun现在是"不同意"按钮
        btn_buBaoChun.addTouchEventListener(function(sender, type) {
            if (type == 2) {
                this.clearBaoChunCountdown();
                btn_baoChun.visible = false;
                btn_buBaoChun.visible = false;
                cc.log("庄家点击不同意报春");

                // 播放不同意音效
                playBaoChunEffect_chuntian(2, SelfUid()); // 2=不同意

                MjClient.gamenet.request("pkroom.handler.tableMsg", {
                    cmd: "baoChun",
                    agree: false,
                    isZhuangConfirm: true
                });
            }
        }.bind(this));
    }
};

PlayPanel_chuntian.prototype.startBaoChunResult=function(msg) {
    cc.log("=== startBaoChunResult函数开始执行 ===");
    cc.log("传入的msg:", JSON.stringify(msg));

    // 清除倒计时
    this.clearBaoChunCountdown();

    // 恢复出牌按钮可用
    var btnPutCard =  MjClient.playui.jsBind.BtnPutCard._node;
    if(btnPutCard) btnPutCard.setEnabled ? btnPutCard.setEnabled(true) : (btnPutCard.enabled = true);

    // 隐藏报春选择/庄家确认按钮
    var isbaochunNode = MjClient.playui.jsBind.isbaochun;
    if (isbaochunNode) {
        var btn_baoChun = isbaochunNode.btn_baoChun._node;
        var btn_buBaoChun = isbaochunNode.btn_buBaoChun._node;
        if (btn_baoChun) {
            btn_baoChun.visible = false;
            cc.log("✅ 隐藏报春按钮");
        }
        if (btn_buBaoChun) {
            btn_buBaoChun.visible = false;
            cc.log("✅ 隐藏不报春按钮");
        }
    }

    // 显示报春玩家标识
    if (msg.isBaoChun && msg.baoChunPlayer) {
        this.showBaoChunIcon(msg.baoChunPlayer);
    }

    // // 显示结果提示
    // cc.log("开始显示结果提示...");
    // cc.log("msg.isBaoChun:", msg.isBaoChun);
    // cc.log("msg.zhuangDisagree:", msg.zhuangDisagree);

    // if (msg.isBaoChun) {
    //     var toastMsg = "玩家【" + (msg.baoChunPlayerName || msg.baoChunPlayer || "未知") + "】报春！";
    //     cc.log("✅ 显示报春成功提示:", toastMsg);
    //     MjClient.showToast(toastMsg);
    // } else if (msg.zhuangDisagree) {
    //     cc.log("✅ 显示庄家不同意提示");
    //     MjClient.showToast("庄家不同意报春，本局正常进行。");
    // } else {
    //     cc.log("✅ 显示无人报春提示");
    //     MjClient.showToast("无人报春，本局正常进行。");
    // }

    // cc.log("=== startBaoChunResult函数执行完毕 ===");
}

// 显示报春玩家标识
PlayPanel_chuntian.prototype.showBaoChunIcon = function(baoChunPlayerUid) {
    cc.log("=== 显示报春标识 ===", "玩家UID:", baoChunPlayerUid);

    var sData = MjClient.data.sData;
    var tData = sData.tData;

    // 获取报春玩家的UI偏移量
    var uiOff = card_getUiOffByUid(baoChunPlayerUid, tData.uids);
    cc.log("报春玩家UI偏移量:", uiOff);

    // 获取玩家节点
    var playerNode = getNode_cards(uiOff);
    if (!playerNode) {
        cc.log("❌ 无法获取玩家节点");
        return;
    }

    // 获取头像节点
    var headNode = playerNode.getChildByName("head");
    if (!headNode) {
        cc.log("❌ 无法获取头像节点");
        return;
    }

    // 移除之前的报春标识（如果存在）
    var existingIcon = headNode.getChildByName("baoChunIcon");
    if (existingIcon) {
        existingIcon.removeFromParent(true);
    }

    // 创建报春标识
    var baoChunIcon = new cc.Sprite("playing/chuntiangametable/ctpk_avatar_baochun.png");
    if (!baoChunIcon) {
        cc.log("❌ 无法加载报春图标资源");
        return;
    }

    baoChunIcon.setName("baoChunIcon");
    baoChunIcon.setAnchorPoint(0.5, 0.5);

    // 设置位置（在头像右上角）
    var headSize = headNode.getContentSize();
    baoChunIcon.setPosition(headSize.width * 0.8, headSize.height * 0.8);

    // 设置层级，确保在最上层
    baoChunIcon.setLocalZOrder(1000);

    // 添加到头像节点
    headNode.addChild(baoChunIcon);

    cc.log("✅ 报春标识显示成功");
};

// 隐藏所有报春标识
PlayPanel_chuntian.prototype.hideAllBaoChunIcons = function() {
    var tData = MjClient.data.sData.tData;

    for (var i = 0; i < tData.maxPlayer; i++) {
        var playerNode = getNode_cards(i);
        if (playerNode) {
            var headNode = playerNode.getChildByName("head");
            if (headNode) {
                var baoChunIcon = headNode.getChildByName("baoChunIcon");
                if (baoChunIcon) {
                    baoChunIcon.removeFromParent(true);
                }
            }
        }
    }
};

PlayPanel_chuntian.prototype.showGameRule = function () {

    var tData = MjClient.data.sData.tData;
    var str = "";

    str += "人数：";
    str += MjClient.MaxPlayerNum == 3 ? "三人玩," : "两人玩,";
    str += "牌数：";
    str += tData.areaSelectMode.cardNumIndex == 0 ? "16张," : "15张,";
    str += tData.areaSelectMode.mustPut == 0 ? 0 : "";
    str += tData.areaSelectMode.zhaDanBuChai ? "炸弹不可拆," : "";

    var firstOutRuleStr = "";
    switch (tData.areaSelectMode.firstPutRule){
        case 1:{
            firstOutRuleStr = tData.areaSelectMode.isPreRoundFirstRule==true?  "每局先出黑桃3,":"首局先出黑桃3,"
            break;
        }
        case 2:{
            firstOutRuleStr = ""
            break;
        }
        case 3:{
            firstOutRuleStr = tData.areaSelectMode.isPreRoundFirstRule==true?  "每局先出黑桃3,":"首局先出黑桃3,"
            break;
        }
        case 4:{
            firstOutRuleStr = tData.areaSelectMode.isPreRoundFirstRule==true?  "每局随机先手,":"首局随机先手,"
            break;
        }
        default:{
            firstOutRuleStr = ""
            break
        }
    };

    str += "飘分：";
    switch (tData.areaSelectMode.piaofen){
        case 1:{ str += "飘123,"; break; }
        case 2:{ str += "飘235,"; break; }
        case 3:{ str += "飘258,"; break; }
        case 4:{ str += "每局飘1,"; break; }
        case 5:{ str += "每局飘2,"; break; }
        default:{ str += "不飘,"; break }
    };

    str += firstOutRuleStr;
    str += tData.areaSelectMode.can4dai2 ? "四带二," : "";
    str += tData.areaSelectMode.can4dai3 ? "四带三," : "";
    str += tData.areaSelectMode.hongTao10Niao ? "红桃10扎鸟," : "";
    str += tData.areaSelectMode.hongTao10JiaFen ? "红桃10加5分," :"";
    str += tData.areaSelectMode.can3aZhaDan ? "3个A算炸弹," : "";

    str += "切牌：";
    str += tData.areaSelectMode.isPlayerShuffle == 1 ? "手动切牌," : "系统切牌,";

    if (typeof(tData.areaSelectMode.fengDing) == "number") {
        str += "封顶：";
        switch (tData.areaSelectMode.fengDing)
        {
            case 1:
                str += "30/32分封顶,";
                break;
            case 2:
                str += "60/64分封顶,";
                break;
            case 3:
                str += "120/128分封顶,"; 
                break;
            default:
                str += "不封顶,";
                break;
        }
    }

    // str += tData.areaSelectMode.paodekuaiTY_difen ? "底分x" + tData.areaSelectMode.paodekuaiTY_difen + ","  : "";
    str += tData.areaSelectMode.fangQiangGuan ? "防强关," : "";
    str += tData.areaSelectMode.baoDanPutMax ? "报单必大," : "";

    str += tData.areaSelectMode.fangZuoBi ? "防作弊," : "";
    str += tData.areaSelectMode.showCardNumber ? "显示牌数," : "";

    str += "翻倍：";
    if(tData.areaSelectMode.fanBei == 1)
    {
        str += "小于" + tData.areaSelectMode.fanBeiScore + "分翻倍,";
    } else {
        str += "不翻倍,";
    }

    // if(tData.areaSelectMode.trustTime > 0)
    // {
    //     str += Math.floor(tData.areaSelectMode.trustTime/60) + "分钟,";
    // } 

    // if(tData.areaSelectMode.isTrustWhole && tData.areaSelectMode.trustWay >= 0)
    // {
    //     str += ["托管当局,", "托管当局+下一局,", "整场托管,"][tData.areaSelectMode.trustWay] || "";
    // }

    if (tData.areaSelectMode.trustTime > 0) {
        var strTuoguan = "托管类型：";
        switch (tData.areaSelectMode.trustWay) {
            case 0:
                strTuoguan = (strTuoguan + "当局托管,");
                break;
            case 1:
                strTuoguan = (strTuoguan + "当局+下一局,");
                break;
            case 2:
                strTuoguan = (strTuoguan + "整场托管,");
                break;
            default:
                strTuoguan += "无,";
        }
        str = str + strTuoguan;
    }

    var strTuoguan = "托管：";
    if (tData.areaSelectMode.trustTime > 0) {
        strTuoguan += (Math.floor(tData.areaSelectMode.trustTime) + "秒后托管,");
    } else {
        strTuoguan += "无托管,";
    }
    str = str + strTuoguan;

    str = (str + (("底分：" + tData.areaSelectMode.paodekuaiTY_difen) + "分,"));

    var strPayWay = "支付：";
    switch (tData.areaSelectMode.payWay) {
        case 0:
            strPayWay += "房主付";
            break;
        case 1:
            strPayWay += "AA付";
            break;
        case 2:
            strPayWay += "大赢家付";
            break;
        default:
            strPayWay += "无";
    }
    str = str + strPayWay;

    if (str.charAt(str.length - 1) == ",")
        str = str.substring(0, str.length - 1);

    return str;

};

// 添加手牌状态检查方法，防止oHands为null的错误
PlayPanel_chuntian.prototype.checkHandsValid = function(oHands) {
    if (!oHands || !Array.isArray(oHands)) {
        console.warn("oHands is null or not array, returning empty array");
        return [];
    }
    return oHands;
};

// 重写UpdataCurrentPutCard方法，添加手牌状态检查
PlayPanel_chuntian.prototype.UpdataCurrentPutCard = function() {
    var mySelf = getUIPlayer(0);
    if (!mySelf || !mySelf.mjhand) {
        console.warn("mySelf or mySelf.mjhand is null");
        return;
    }
    
    // 检查手牌是否有效
    var validHands = this.checkHandsValid(mySelf.mjhand);
    if (validHands.length === 0) {
        console.warn("No valid hands found");
        return;
    }
    
    // 调用原有的出牌按钮更新逻辑
    // 这里可以添加对新牌型的支持
    var selectedCards = MjClient.selectTipCardsArray || [];
    if (selectedCards.length > 0) {
        var cardType = MjClient.majiang_chuntian.calType(selectedCards, MjClient.data.sData.tData.areaSelectMode);
        
        // 检查是否为新牌型
        if (cardType == MjClient.majiang_chuntian.CARDTPYE.santiao ||
            cardType == MjClient.majiang_chuntian.CARDTPYE.sidaiyi ||
            cardType == MjClient.majiang_chuntian.CARDTPYE.danshun) {
            
            // 启用出牌按钮
            if (this._btnPutCard) {
                this._btnPutCard.setEnabled(true);
                this._btnPutCard.setBright(true);
            }
        }
    }
    
    // 调用父类方法
    if (PlayLayer_PDK.prototype.UpdataCurrentPutCard) {
        PlayLayer_PDK.prototype.UpdataCurrentPutCard.call(this);
    }
};

// ====== 庄家标识显示功能 ======

// 显示庄家标识
PlayPanel_chuntian.prototype.showZhuangIcon = function(UIoff) {
    var tData = MjClient.data.sData.tData;

    // 检查游戏状态
    if (tData.tState == TableState.isReady || tData.tState == TableState.roundFinish) {
        return;
    }

    var pl = getUIPlayer(UIoff);
    if (!pl) {
        return;
    }

    var node_cards = getNode_cards(UIoff);
    if (!node_cards) {
        return;
    }

    var headNode = node_cards.getChildByName("head");
    if (!headNode) {
        return;
    }

    var zhuangNode = headNode.getChildByName("zhuang");
    if (!zhuangNode) {
        return;
    }

    // 检查是否是庄家
    var isZhuang = (tData.uids[tData.zhuang] == pl.info.uid);

    if (isZhuang) {
        zhuangNode.loadTexture("playing/gameTable/youxizhong-1_89.png");
        zhuangNode.visible = true;
        zhuangNode.zIndex = 99;

        // 隐藏连庄标识
        var linkZhuang = zhuangNode.getChildByName("linkZhuang");
        if (linkZhuang) {
            linkZhuang.setVisible(false);
        }
    } else {
        zhuangNode.visible = false;
    }
};

// 更新所有玩家的庄家标识
PlayPanel_chuntian.prototype.updateAllZhuangIcons = function() {
    for (var i = 0; i < MjClient.MaxPlayerNum; i++) {
        this.showZhuangIcon(i);
    }
};

// ====== 报春倒计时相关函数 ======
// 开始报春倒计时
PlayPanel_chuntian.prototype.startBaoChunCountdown = function(timeLimit) {
    this.clearBaoChunCountdown();

    var countdownTime = timeLimit || 15;
    this.baoChunCountdownTime = countdownTime;

    // 创建倒计时显示文本
    this.createCountdownText("报春选择", countdownTime);

    // 开始倒计时
    this.baoChunCountdownTimer = setInterval(function() {
        this.baoChunCountdownTime--;
        this.updateCountdownText(this.baoChunCountdownTime);

        if (this.baoChunCountdownTime <= 0) {
            this.clearBaoChunCountdown();
            // 自动选择不报春
            var isbaochunNode = MjClient.playui.jsBind.isbaochun;
            if (isbaochunNode) {
                var btn_baoChun = isbaochunNode.btn_baoChun._node;
                var btn_buBaoChun = isbaochunNode.btn_buBaoChun._node;
                if (btn_baoChun && btn_buBaoChun) {
                    btn_baoChun.visible = false;
                    btn_buBaoChun.visible = false;

                    // 播放不报春音效
                    playBaoChunEffect_chuntian(0, SelfUid()); // 0=不报

                    // 发送不报春消息到服务器
                    MjClient.gamenet.request("pkroom.handler.tableMsg", {
                        cmd: "baoChun",
                        isBaoChun: false
                    });
                }
            }
        }
    }.bind(this), 1000);
};

// 开始庄家确认倒计时
PlayPanel_chuntian.prototype.startZhuangConfirmCountdown = function(timeLimit) {
    this.clearBaoChunCountdown();

    var countdownTime = timeLimit || 15;
    this.baoChunCountdownTime = countdownTime;

    // 创建倒计时显示文本
    this.createCountdownText("庄家确认", countdownTime);

    // 开始倒计时
    this.baoChunCountdownTimer = setInterval(function() {
        this.baoChunCountdownTime--;
        this.updateCountdownText(this.baoChunCountdownTime);

        if (this.baoChunCountdownTime <= 0) {
            this.clearBaoChunCountdown();
            // 自动选择同意
            var isbaochunNode = MjClient.playui.jsBind.isbaochun;
            if (isbaochunNode) {
                var btn_baoChun = isbaochunNode.btn_baoChun._node;
                var btn_buBaoChun = isbaochunNode.btn_buBaoChun._node;
                if (btn_baoChun && btn_buBaoChun) {
                    btn_baoChun.visible = false;
                    btn_buBaoChun.visible = false;

                    // 播放同意音效
                    playBaoChunEffect_chuntian(3, SelfUid()); // 3=同意

                    // 发送同意消息到服务器
                    MjClient.gamenet.request("pkroom.handler.tableMsg", {
                        cmd: "baoChun",
                        agree: true,
                        isZhuangConfirm: true
                    });
                }
            }
        }
    }.bind(this), 1000);
};

// 清除报春倒计时
PlayPanel_chuntian.prototype.clearBaoChunCountdown = function() {
    if (this.baoChunCountdownTimer) {
        clearInterval(this.baoChunCountdownTimer);
        this.baoChunCountdownTimer = null;
    }
    this.hideCountdownText();
};

// 创建倒计时显示文本
PlayPanel_chuntian.prototype.createCountdownText = function(title, time) {
    var scene = cc.director.getRunningScene();
    if (!scene) return;

    // 移除之前的倒计时文本
    this.hideCountdownText();

    // 创建倒计时背景
    this.countdownBg = new cc.Scale9Sprite("playing/chuntiangametable/paodekuai_di1.png");
    this.countdownBg.setContentSize(cc.size(200, 60));
    this.countdownBg.setPosition(cc.winSize.width / 2, cc.winSize.height - 100);
    scene.addChild(this.countdownBg, 1000);

    // 创建倒计时文本
    this.countdownText = new cc.LabelTTF(title + ": " + time + "秒", "Arial", 24);
    this.countdownText.setColor(cc.color(255, 255, 255));
    this.countdownText.setPosition(cc.winSize.width / 2, cc.winSize.height - 100);
    scene.addChild(this.countdownText, 1001);
};

// 更新倒计时显示文本
PlayPanel_chuntian.prototype.updateCountdownText = function(time) {
    if (this.countdownText) {
        var title = time > 0 ? (this.baoChunCountdownTime > 10 ? "请选择" : "请选择") : "";
        this.countdownText.setString(title + ": " + time + "秒");

        // 时间不足5秒时变红色警告
        if (time <= 5 && time > 0) {
            this.countdownText.setColor(cc.color(255, 0, 0));
        } else {
            this.countdownText.setColor(cc.color(255, 255, 255));
        }
    }
};

// 隐藏倒计时显示文本
PlayPanel_chuntian.prototype.hideCountdownText = function() {
    if (this.countdownText) {
        this.countdownText.removeFromParent();
        this.countdownText = null;
    }
    if (this.countdownBg) {
        this.countdownBg.removeFromParent();
        this.countdownBg = null;
    }
};

/**
 * 春天扑克出牌音效函数
 * @param {Array} cards 出的牌
 * @param {String} uid 出牌玩家的uid
 * @param {Boolean} isLocal 是否是本地播放
 */
function playSoundEffect_chuntian(cards, uid, isLocal) {
    // 如果是自己出牌且不是本地播放且不是回放，则跳过（避免重复播放）
    if (uid == SelfUid() && !isLocal && MjClient.rePlayVideo == -1) {
        return;
    }

    if (!cards || cards.length == 0) {
        return;
    }

    var sData = MjClient.data.sData;
    var tData = sData.tData;
    var pl = sData.players[uid];
    if (!pl) {
        return;
    }
    var sex = pl.info.sex;

    // 计算点数的函数
    function calPoint(card) {
        var point = Math.floor((card - 1) / 4) + 1;
        if (point == 1) return 14; // A
        if (point == 2) return 15; // 2
        return point;
    }

    var url = "chuntian/";

    // 根据性别选择音效路径
    if (sex == 1) {
        url += "nan/";
    } else {
        url += "nv/";
    }

    // 获取牌型
    var cardType = MjClient.majiang_chuntian.calType(cards, tData.areaSelectMode);

    // 获取主要牌的点数（用于音效）
    var mainPoint = calPoint(cards[0]);

    // 将点数映射到音效文件的格式
    var soundPoint = mainPoint;
    if (mainPoint == 14) soundPoint = 1;  // A -> 1
    else if (mainPoint == 15) soundPoint = 2;  // 2 -> 2
    else if (mainPoint >= 3 && mainPoint <= 13) soundPoint = mainPoint;  // 3-K保持不变

    var soundFile = "";

    // 根据牌型选择音效文件
    switch (cardType) {
        case MjClient.majiang_chuntian.CARDTPYE.danpai:
            // 单张：1-点数
            soundFile = "1-" + soundPoint;
            break;
        case MjClient.majiang_chuntian.CARDTPYE.duizi:
            // 对子：2-点数
            soundFile = "2-" + soundPoint;
            break;
        case MjClient.majiang_chuntian.CARDTPYE.sanzhang:
            // 三条：13-点数
            soundFile = "13-" + soundPoint;
            break;
        case MjClient.majiang_chuntian.CARDTPYE.shunzi:
            // 顺子：3-0
            soundFile = "3-0";
            break;
        case MjClient.majiang_chuntian.CARDTPYE.liandui:
            // 姊妹对：4-2
            soundFile = "4-2";
            break;
        case MjClient.majiang_chuntian.CARDTPYE.tuituji:
            // 推土机：4-3
            soundFile = "4-3";
            break;
        case MjClient.majiang_chuntian.CARDTPYE.sandaier:
            // 三带二：7-0
            soundFile = "7-0";
            break;
        case MjClient.majiang_chuntian.CARDTPYE.sidaiyi:
            // 四带一：9-0
            soundFile = "9-0";
            break;
        case MjClient.majiang_chuntian.CARDTPYE.sizha:
            // 炸弹：15-0
            soundFile = "15-0";
            break;
        case MjClient.majiang_chuntian.CARDTPYE.sandaiyi:
            // 三带一：使用三条的音效 13-点数
            soundFile = "13-" + soundPoint;
            break;
        default:
            // 默认使用单张音效
            soundFile = "1-" + soundPoint;
            break;
    }

    url += soundFile;
    playEffect(url, false, sex);
}

/**
 * 春天扑克报春音效函数
 * @param {Number} action 报春动作：0=不报，1=报春，2=不同意，3=同意
 * @param {String} uid 玩家uid
 */
function playBaoChunEffect_chuntian(action, uid) {
    var sData = MjClient.data.sData;
    if (!sData) return;

    var pl = sData.players[uid];
    if (!pl) return;

    var sex = pl.info.sex;
    var url = "chuntian/";

    // 根据性别选择音效路径
    if (sex == 1) {
        url += "nan/";
    } else {
        url += "nv/";
    }

    url += "baochun/" + action;

    cc.log("=========playBaoChunEffect_chuntian======", url, "action:", action);
    playEffect(url, false, sex);
}

/**
 * 春天扑克春天状态音效函数
 * @param {Number} chunType 春天类型：0-6对应不同状态
 * @param {String} uid 玩家uid
 */
function playChunEffect_chuntian(chunType, uid) {
    var sData = MjClient.data.sData;
    if (!sData) return;

    var pl = sData.players[uid];
    if (!pl) return;

    var sex = pl.info.sex;
    var url = "chuntian/";

    // 根据性别选择音效路径
    if (sex == 1) {
        url += "nan/";
    } else {
        url += "nv/";
    }

    url += "chun/" + chunType;

    cc.log("=========playChunEffect_chuntian======", url, "chunType:", chunType);
    playEffect(url, false, sex);
}

/**
 * 春天扑克报单音效函数
 * @param {String} uid 玩家uid
 */
function playBaoDanEffect_chuntian(uid) {
    var sData = MjClient.data.sData;
    if (!sData) return;

    var pl = sData.players[uid];
    if (!pl) return;

    var sex = pl.info.sex;
    var url = "chuntian/";

    // 根据性别选择音效路径
    if (sex == 1) {
        url += "nan/";
    } else {
        url += "nv/";
    }

    url += "baodan";

    cc.log("=========playBaoDanEffect_chuntian======", url);
    playEffect(url, false, sex);
}

/**
 * 春天扑克过牌音效函数
 * @param {String} uid 玩家uid
 */
function playPassEffect_chuntian(uid) {
    var sData = MjClient.data.sData;
    if (!sData) return;

    var pl = sData.players[uid];
    if (!pl) return;

    var sex = pl.info.sex;
    var url = "chuntian/";

    // 根据性别选择音效路径
    if (sex == 1) {
        url += "nan/";
    } else {
        url += "nv/";
    }

    url += "pass";

    cc.log("=========playPassEffect_chuntian======", url);
    playEffect(url, false, sex);
}


